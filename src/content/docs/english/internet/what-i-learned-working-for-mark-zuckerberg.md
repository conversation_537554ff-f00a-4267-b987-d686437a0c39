---
title: What I Learned Working For <PERSON>
description: What I Learned Working For <PERSON>
template: doc
tableOfContents: false
lastUpdated: 2024-09-13 09:27:13
---

[原文](https://noahkagan.com/what-i-learned-working-for-mark-zuckerberg/)

When I walked into the first floor of the Facebook building on University Avenue, in Palo Alto, I wasn’t sure if I was in a frat house or a startup.

There were cables falling from the ceilings, people running around and I was told to sit on a corner of someone else’s desk.

My new boss walked by me and said he’d find me after lunch. Cool! Then someone gave me a laptop and I just started playing on the web until I was later told to prepare for an impromptu meeting with <PERSON> in 30 minutes.

<PERSON> comes into the meeting and says:

“I just fired your boss, welcome to Facebook. You’ll be fine here if you don’t try to sell my company behind my back.”

And this is when the fun really began…

Working at Facebook was the best (and worst thing) to ever happen to me.

I was employee #30. Then I got fired 9 months later.

For a long time, I was bitter that they let me go.

But the lessons I learned from <PERSON> and <PERSON> ended up helping me build AppSumo into a $100 million per year company.

10 non-obvious lessons I learned from working directly under <PERSON>:

![](/english/internet/what-i-learned-working-for-mark-zuckerberg-1.png)

### 1) Focus on ONE goal
“Mark, we’re not profitable. Let’s try selling tickets inside Facebook events,” I pleaded.

He said no.

Then he took a dry-erase marker and wrote on the board: GROWTH.

Mark’s goal was 1 billion users.

Every idea we’d bring, he’d ask, “Does this help growth or not?”

If it wasn’t driving toward that goal, we didn’t do it.

You don’t grow fast by doing many things, but by doing ONE thing extremely well.

### 2) Move fast
At Facebook, it was normal to work 12+ hours a day.

![A long night.](/english/internet/what-i-learned-working-for-mark-zuckerberg-2.png)



Mark constantly pushed us to have a sense of urgency. One of his internal mantras was “Move fast and break things”.

“Unless you are breaking stuff, you are not moving fast enough.” he’d say.

The idea was that we would tolerate some amount of bugs and flaws in the service of moving faster and learning what our community wanted faster.

We shipped several updates to the site every day. In comparison, companies like Microsoft would take months to write out product details, discuss them in a lot of meetings, and finally build them.

As a startup, your biggest advantage against giant companies is speed.

### 3) Only hire A PLUS players
Mark would only hire people he would be happy to work for.

Even our customer support team was filled with Harvard PhD’s.

The people from Facebook have gone on to help found Asana, Quora, AppSumo 😉, OpenAI, and more.

When you’re in a startup, the first ten people you hire are the most critical. Each makes up 10% of the company. If three are not great, that’s 30% of your company!

A startup depends on great people much more than a big company.

### 4) Treat your employees well
Mark recognized that having a work environment you want to work in would appeal to potential employees and make the existing ones proud to be there (and stay later at night).

Facebook did a lot of things that are the norm now:

1. A fancy office in one of the most expensive neighborhoods of Silicon Valley
2. Hyper competitive salaries
3. $1000 office chairs for everyone
4. Comped PowerBook and BlackBerry
5. Delicious breakfast, lunch, and dinner catered
6. Fridge stocked with any drink you can imagine
7. All expenses paid company trips to Las Vegas
8. Free happy hours every Friday
9. Free laundry/dry cleaning service
10. Subsidized housing. $600/month if you lived within 1 mile of the office.
11. Summer housing/Winter Cabin that anyone could use
    
People want to feel acknowledged. Treating your employees well improves work and boosts morale.
![Early Facebook party](/english/internet/what-i-learned-working-for-mark-zuckerberg-3.png)



Early Facebook party

### 5) Scratch your own itch
Many people start businesses in a category they don’t know much about or have an interest in because they’re told it’s “hot” or “trending”.

They have a job as an accountant but try to start a business making software for Content Creators.

At the start, Mark never intended to build a company. He was just trying to help connect people at college.

I started AppSumo because I loved tech products and deals.

Many of the top companies didn’t set out to become companies. The founders solved a problem they faced themselves, and then shared the solution with others.

Build selfishly, share selflessly.

### 6) Pay attention to details
I remember Mark sent me an email at 3 am telling me that I missed a period in one of our documents. A period (!!)

Mark didn’t accept anything less than perfect. If he thought something was shit he would tell you and you’d have to start over.

He was meticulous about capitalizing the “F” in Facebook. Mark even gifted me the book Elements of Style (a grammar book) for Hanukkah 😂

Mark set a high standard of excellence for us. It was challenging, but also super rewarding.

### 7) Give ownership to the team
Surprisingly, Mark wasn’t super involved in the day-to-day operations. He coded some of the time, but mostly was focused on the macro vision.

He was great about giving people a goal, some boundaries, and coaching them from the sidelines. 

Engineers and product managers could come up with features and build them out without needing anyone’s approval. 

Mark said he wanted Facebook mobile, and he let us figure out the details to make the very first version. 

When your team feels like an owner, they will act as an owner.

### 8) “People” not “Users”
Mark would yell at us if we said “users”. Like literally yell.

“They’re human beings”, he’d scream.

Humanizing the people who use your products allows you to serve them better. You’re able to better relate to the problems they’re facing vs just looking at numbers.

On the other side of that username or email address is a fellow human!

### 9) Keep the right people on the bus
My boss was fired the day I started. My next boss was fired a month later. I got fired in 9 months. 

Mark was intense about keeping the right people on the bus.

He removed the people that were holding Facebook back immediately and he quickly promoted the ones that were helping Facebook achieve its goals

At AppSumo, we run paid trials with potential teammates before bringing them on full-time to ensure they’re the right fit.

### 10) Have a big-ass vision
We were all in our 20s when Mark was offered $1B to sell Facebook.

When he said no, he sent a message to all of us and the world.

His goal was to connect the ENTIRE world. That inspired the shit out of us.

When I was at Facebook all I did was think/talk/dream about Facebook. Facebook was my girlfriend. It didn’t feel like a job, so I put in all my hours.

A big vision is what motivates people to get up and come give their best at the office. It gives employees a sense of purpose beyond money.

 

Rooting for you,

Noah 🌮
