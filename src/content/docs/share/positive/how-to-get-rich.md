---
title: How to Get Rich
description: How to Get Rich
template: doc
# tableOfContents: false
lastUpdated: 2024-07-14
---

:::note[中文翻译]
[如何致富](/share/positive/how-to-get-rich-zh)
:::

:::tip[原文]
[https://nav.al/rich](https://nav.al/rich)
:::


_A collection of all my interviews about my ‘How to Get Rich’ [tweetstorm](https://twitter.com/naval/status/1002103360646823936)._ 

## Seek Wealth, Not Money or Status

_Wealth is assets that earn while you sleep_

**<PERSON> is a prolific tech investor and founder of AngelList**

**Nivi:** You probably know <PERSON> from his [Twitter](https://twitter.com/naval) account.

We’re going to talk about his tweetstorm, “[How To Get Rich (without getting lucky)](https://twitter.com/naval/status/1002103360646823936).” We’ll go through most of the tweets in detail, give <PERSON> a chance to expand on them and generally riff on the topic. He’ll probably throw in ideas he hasn’t published before.

<PERSON>’s the co-founder of [AngelList](http://angel.co/) and Epinions. He’s also a prolific tech investor in companies like Twitter, Uber and many more.

[I’m](http://twitter.com/nivi) the co-founder of <PERSON>List with Naval. And I co-authored the [Venture Hacks](http://venturehacks.com/) blog with him back in the day.

**Naval:** The “How to Get Rich” tweetstorm definitely hit a nerve and went viral. A lot of people say it was helpful and reached across aisles.

People outside of the tech industry—people in all walks of life—want to know how to solve their money problems. Everyone vaguely knows they want to be wealthy, but they don’t have a good set of principles to do it by.

**Wealth is assets that earn while you sleep**

**Nivi:** What’s the difference between wealth, money and status?

**Naval:** Wealth is the thing you want. Wealth is assets that earn while you sleep; it’s the factory of robots cranking out things. Wealth is the computer program running at night that’s serving other customers. Wealth is money in the bank that is reinvested into other assets and businesses.

A house can be a form of wealth, because you can rent it out; although that’s a less productive use of land than running a commercial enterprise.

My definition of wealth is oriented toward businesses and assets that can earn while you sleep.

**Wealth buys your freedom**

You want wealth because it buys you freedom—so you don’t have to wear a tie like a collar around your neck; so you don’t have to wake up at 7:00 a.m. to rush to work and sit in commute traffic; so you don’t have to waste your life grinding productive hours away into a soulless job that doesn’t fulfill you.

The purpose of wealth is freedom; it’s nothing more than that. It’s not to buy fur coats, or to drive Ferraris, or to sail yachts, or to jet around the world in a Gulf Stream. That stuff gets really boring and stupid, really fast. It’s about being your own sovereign individual.

You’re not going to get that unless you really want it. The entire world wants it, and the entire world is working hard at it.

It is competitive to some extent. It’s a positive sum game—but there are competitive elements to it, because there’s a finite amount of resources right now in society. To get the resources to do what you want, you have to stand out.

**Money is how we transfer wealth**

Money is how we transfer wealth. Money is social credits; it’s the ability to have credits and debits of other people’s time.

If I do my job right and create value for society, society says, “Oh, thank you. We owe you something in the future for the work that you did. Here’s a little IOU. Let’s call that money.”

That money gets debased because people steal the IOUs; the government prints extra IOUs; and people renege on their IOUs. But money tries to be a reliable IOU from society that you are owed something for something you did in the past.

We transfer these IOUs around; money is how we transfer wealth.

**Status is your rank in the social hierarchy**

There are fundamentally two huge games in life that people play. One is the money game. Money is not going to solve _all_ of your problems; but it’s going to solve all of your _money_ problems. I think people know that. They realize that, so they want to make money.

At the same time, deep down many people believe they can’t make it; so they don’t want any wealth creation to happen. They virtue signal by attacking the whole enterprise, saying, “Well, making money is evil. You shouldn’t do it.”

But they’re actually playing the other game, which is the status game. They’re trying to be high status in the eyes of others by saying, “Well, I don’t need money. We don’t want money.” 

Status is your ranking in the social hierarchy.

Wealth is not a zero-sum game. Everybody in the world can have a house. Because you have a house doesn’t take away from my ability to have a house. If anything, the more houses that are built, the easier it becomes to build houses, the more we know about building houses, and the more people can have houses.

Wealth is a very positive-sum game. We create things together. We’re starting this endeavor to create a piece of art that explains what we’re doing. At the end of it, something brand new will be created. It’s a positive-sum game.

**Status is a very old game**

Status, on the other hand, is a zero-sum game. It’s a very old game. We’ve been playing it since monkey tribes. It’s hierarchical. Who’s number one? Who’s number two? Who’s number three? And for number three to move to number two, number two has to move out of that slot. So, status is a zero-sum game.

Politics is an example of a status game. Even sports is an example of a status game. To be the winner, there must be a loser. Fundamentally, I don’t like status games. They play an important role in our society, so we can figure out who’s in charge. But you play them because they’re a necessary evil.

On an evolutionary basis—if you go back thousands of years—status is a much better predictor of survival than wealth. You couldn’t have wealth before the farming age because you couldn’t store things. Hunter-gatherers carried everything on their backs.

Hunter-gatherers lived in entirely status-based societies. Farmers started going to wealth-based societies. The modern industrial economies are much more heavily wealth-based societies.

**People creating wealth will always be attacked by people playing status games**

There’s always a subtle competition going on between status and wealth. For example, when journalists attack rich people or the tech industry, they’re really bidding for status. They’re saying, “No, the _people_ are more important. And I, the journalist, represent the people, and therefore I am more important.”

The problem is, to win at a status game you have to put somebody else down. That’s why you should avoid status games in your life—because they make you into an angry combative person. You’re always fighting to put other people down and elevate yourself and the people you like.

Status games are always going to exist; there’s no way around it. Realize that most of the time when you’re trying to create wealth, you’re getting attacked by someone else and they’re trying to look like a goody-two shoes. They’re trying to up their own status at your expense.

They’re playing a different game. And it’s a worse game. It’s a zero-sum game, instead of a positive-sum game.

## Make Abundance for the World

_Wealth isn’t about taking something from somebody els_e

**Ethical wealth creation makes abundance for the world**

**Naval:** I think there is this notion that making money is evil, right? It’s rooted all the way back down to “money is the root of all evil.” People think that the bankers steal our money. It’s somewhat true in that, in a lot of the world, there’s a lot of theft going on all the time.

The history of the world, in some sense, is this predator/prey relationship between makers and takers. There are people who go out and create things, and build things, and work hard on things.

Then there are people who come along with a sword, or a gun, or taxes, or crony capitalism, or Communism, or what have you. There’s all these different methods to steal.

Even in nature, there are more parasites than there are non-parasitical organisms. You have a ton of parasites in you, who are living off of you. The better ones are symbiotic, they’re giving something back. But there are a lot that are just taking. That’s the nature of how any complex system is built.

What I am focused on is true wealth creation. It’s not about taking money. It’s not about taking something from somebody else. It’s from creating abundance.

Obviously, there isn’t a finite number of jobs, or finite amount of wealth. Otherwise we would still be sitting around in caves, figuring out how to divide up pieces of fire wood, and the occasional dead deer.

Most of the wealth in civilization, in fact all of it, has been created. It got created from somewhere. It got created from people. It got created from technology. It got created from productivity. It got created from hard work. This idea that it’s stolen is this horrible zero-sum game that people who are trying to gain status play.

**Everyone can be rich**

But the reality is everyone can be rich. We can see that by seeing, that in the First World, everyone is basically richer than almost anyone who was alive 200 years ago.

200 years ago nobody had antibiotics. Nobody had cars. Nobody had electricity. Nobody had the iPhone. All of these things are inventions that have made us wealthier as a species.

Today, I would rather be a poor person in a First World country, than be a rich person in Louis the XIV’s France. I’d rather be a poor person today than aristocrat back then. That’s because of wealth creation.

The engine of technology is science that is applied for the purpose of creating abundance. So, I think fundamentally everybody can be wealthy.

This thought experiment I want you to think through is imagine if everybody had the knowledge of a good software engineer and a good hardware engineer. If you could go out there, and you could build robots, and computers, and bridges, and program them. Let’s say every human knew how to do that.

What do you think society would look like in 20 years? My guess is what would happen is we would build robots, machines, software and hardware to do everything. We would all be living in massive abundance.

We would essentially be retired, in the sense that none of us would have to work for any of the basics. We’d even have robotic nurses. We’d have machine driven hospitals. We’d have self-driving cars. We’d have farms that are 100% automated. We’d have clean energy.

At that point, we could use technology breakthroughs to get everything that we wanted. If anyone is still working at that point, they’re working as a form of expressing their creativity. They’re working because it’s in them to contribute, and to build and design things.

I don’t think capitalism is evil. Capitalism is actually good. It’s just that it gets hijacked. It gets hijacked by improper pricing of externalities. It gets hijacked by improper yields, where you have corruption, or you have monopolies.

## Free Markets Are Intrinsic to Humans

_We use credits and debits to cooperate across genetic boundaries_

**Free markets are intrinsic to the human species**

**Naval:** Overall capitalism \[meaning free markets\] is intrinsic to the human species. Capitalism is not something we invented. Capitalism is not even something we discovered. It is in us in every exchange that we have.

When you and I exchange information, I want some information back from you. I give you information. You give me information. If we weren’t having a good information exchange, you’d go talk to somebody else. So, the notion of exchange, and keeping track of credits and debits, this is built into us as flexible social animals.

We are the only animals in the animal kingdom that cooperate across genetic boundaries. Most animals don’t even cooperate. But when they do, they cooperate only in packs where they co-evolve together, and they share blood, so they have some shared interests.

Humans don’t have that. I can cooperate with you guys. One of you is a Serbian. The other one is a Persian by origin. And I’m Indian by origin. We have very little blood in common, basically none. But we still cooperate.

What lets us cooperate? It’s because we can keep track of debits and credits. Who put in how much work? Who contributed how much? That’s all free market capitalism is.

So, I strongly believe that it is innate to the human species, and we are going to create more and more wealth, and abundance for everybody.

Everybody can be wealthy. Everybody can be retired. Everybody can be successful. It is merely a question of education and desire. You have to want it. If you don’t want it, that’s fine. Then you opt out of the game.

But don’t try to put down the people who are playing the game. Because that’s the game that keeps you in a comfortable warm bed at night. That’s the game that keeps a roof over your head. That’s the game that keeps your supermarkets stocked. That’s the game that keeps the iPhone buzzing in your pocket.

So, it is a beautiful game that is worth playing ethically, rationally, morally, socially for the human race. It’s going to continue to make us all richer and richer, until we have massive wealth creation for anybody who wants it.

**Too many takers and not enough makers will plunge a society into ruin**

**Nivi:** It’s not just individuals secretly despising wealth, right? There are countries, groups, political parties that overtly despise wealth. Or at least seem to.

**Naval:** That’s right. What those countries, political parties, and groups are reduced to is playing the zero-sum game of status. In the process to destroy wealth creation, they drag everybody down to their level.

Which is why the U.S. is a very popular country for immigrants because of the American dream. Anyone can come here, be poor, and then work really hard and make money, and get wealthy. But even just make some basic money for their life.

Obviously, the definition of wealth is different for different people. A First World citizen’s definition of wealth might be, “Oh, I have to make millions of dollars, and I’m completely done.”

Whereas to a Third World poor immigrant just entering the country, and we were poor immigrants who came here when I as fairly young, to the United States, wealth may just be a much lower number. It may just be, “I don’t have to work a manual labor job for the rest of my life that I don’t want to work.”

But groups that despise it will essentially bring the entire group to that level. If you get too many takers, and not enough makers, society falls apart. You end up with a communist country.

Look at Venezuela, right? They were so busy taking, and dividing, and reallocating, that people are literally starving in the streets, and losing kilograms of body weight every year just from sheer starvation.

Another way to think about it is imagine an organism that has too many parasites. You need some small number of parasites to stay healthy.

You need a lot of symbiotes. All the mitochondria in all of our cells that help us respirate and burn oxygen. These are symbiotes that help us survive. We couldn’t survive without them.

But, to me, those are partners in the wealth creation that creates the human body. But if you just were filled with parasites, if you got infected with worms, or a virus, or bacteria that were purely parasitical, you would die.  So, any organism can only withstand a small number of parasites. When the parasitic element gets too far out of control, you die.

Again I’m talking about ethical wealth creation. I’m not talking about monopolies. I’m not talking about crony capitalism. I’m not talking about mispriced externalities like the environment.

I’m talking about free minds, and free markets. Small-scale exchange between humans that’s voluntary, and doesn’t have an outsized impact on others.

I think that kind of wealth creation, if a society does not respect it, if the group does not respect it, then society will plunge into ruin, and darkness.

## Making Money Isn’t About Luck

_Become the kind of person who makes money_

**Making money isn’t about luck**

**Naval:** Obviously, we want to be wealthy, and we want to get there in this lifetime without having to rely on luck.

A lot of people think making money is about luck. It’s not. It’s about becoming the kind of person that makes money.

I like to think that if I lost all my money and if you drop me on a random street in any English-speaking country, within 5, 10 years I’d be wealthy again. Because it’s a skill set that I’ve developed and I think anyone can develop.

In 1,000 parallel universes, you want to be wealthy in 999 of them. You don’t want to be wealthy in the 50 of them where you got lucky. We want to factor luck out of it.

There’s four kinds of luck that we’re talking about. This came from a book. Marc Andreessen, wrote a [blog post](https://pmarchive.com/luck_and_the_entrepreneur.html) about it.

**1\. Blind luck**

The first kind of luck you might say is blind luck. Where I just got lucky because something completely out of my control happened. That’s fortune, that’s fate.

**2\. Luck from hustling**

Then there’s luck that comes through persistence, hard work, hustle, motion. Which is when you’re running around creating lots of opportunities, you’re generating a lot of energy, you’re doing a lot of things, lots of things will get stirred up in the dust.

It’s almost like mixing a petri dish and seeing what combines. Or mixing a bunch of reagents and seeing what combines. You’re generating enough force and hustle and energy that luck will find you.

We, as a group, you could argue, got together because of that. [Nenad](https://m.youtube.com/channel/UCmvhCWvHk3-SJqljh5cCm8A) had put up these great videos online, I saw them on Twitter. In that sense, he generated his own luck by creating videos until people like me keep finding him.

**3\. Luck from preparation**

A third way is that you become very good at spotting luck. If you are very skilled in a field, you will notice when a lucky break happens in that field. When other people who aren’t attuned to it won’t notice. So you become sensitive to luck and that’s through skill and knowledge and work.

**4\. Luck from your unique character**

Then the last kind of luck is the weirdest, hardest kind. But that’s what we want to talk about. Which is where you build a unique character, a unique brand, a unique mindset, where then luck finds you.

For example, let’s say that you’re the best person in the world at deep sea underwater diving. You’re known to take on deep sea underwater dives that nobody else will even attempt to dare.

Then, by sheer luck, somebody finds a sunken treasure ship off the coast. They can’t get it. Well, their luck just became your luck, because they’re going to come to you to get that treasure. You’re going to get paid for it.  

Now, that’s an extreme example. The person who got lucky by finding the treasure chest, that was blind luck. But them coming to you and asking you to extract it and having to give you half, that’s not luck.

You created your own luck. You put yourself in a position to be able to capitalize on that luck. Or to attract that luck when nobody else has created that opportunity for themselves. When we talk about “without getting lucky,” we want to be deterministic, we don’t want to leave it to chance.

**In 1,000 parallel universes, you want to be wealthy in 999 of them**

**Nivi:** Do you want to elaborate a little bit more on the idea that in a 1,000 parallel universes you want to get rich in 999 of them? I think some people are going to see that and say, “that sounds impossible, it sounds like it’s too good to be true.”

**Naval:** No, I don’t think it’s impossible. I think that you may have to work a little bit harder at it given your starting circumstances. I started as a poor kid in India, so if I can make it, anybody can, in that sense.

Now, obviously, I had all my limbs and I had my mental faculties and I did have an education. There are some prerequisites you can’t get past. But if you’re listening to this video or podcast, you probably have the requisite means at your disposal, which is a functioning body and a functioning mind.

And I’ve encountered plenty of bad luck along the way. The first little fortune that I made, I instantly lost in the stock market. The second little fortune that I made, or I should have made, I basically got cheated by my business partners. It’s only the third time around has been a charm.

And, even then, it has been in a slow and steady struggle. I haven’t made money in my life in one giant payout. It’s always been a whole bunch of small things piling up. It’s more about consistently creating wealth by creating businesses, including opportunities and creating investments. It hasn’t been a giant one-off thing.

**Wealth stacks up one chip at a time, not all at once**

My personal wealth has not been generated by one big year. It stacks up little bit, chips at a time. More options, more businesses, more investments, more things that I can do.

Same way that someone like Nenad, illacertus, he’s building his brand online. He’s building videos. It’s not like any one video is going to suddenly shower him with riches overnight. It’s going to be a long lifetime of learning, of reading, of creating that’s going to compound.

We’re talking about getting wealthy so you can retire, so you have your freedom. Not retire in the sense that you don’t do anything. But in the sense that you don’t have to be any place you don’t want to be, you don’t have to do anything you don’t want to do, you can wake up when you want, you can sleep when you want, you don’t have a boss. That’s freedom.

We’re talking about enough wealth to get to freedom. Especially thanks to the Internet these days, though, opportunities are massively abundant. I, in fact, have too many ways to make money, I don’t have enough time. I have opportunities pouring out of my ears and the thing I keep running out of is time.

There’s just so many ways to create wealth, to create products, to create businesses, to create opportunities, and to, as a byproduct, get paid by society that I can’t even handle it all.

## Make Luck Your Destiny

_Build your character in a way that luck becomes deterministic_

**Nivi:** I think it’s pretty interesting that the first three kinds of luck that you described there are very common cliches for them that everybody knows. And then for that last kind of luck that comes to you out of the unique way that you act, there’s no real cliche for it.

So, for the first three kinds, there’s “dumb luck,” or “blind luck.” That’s the first kind of luck. The second kind of luck there’s the cliché that “fortune favors the bold.” That’s a person who gets lucky just by stirring the pot and acting. The third kind of luck, people say that “chance favors the prepared mind.”

But for the fourth kind of luck, there isn’t a common cliché out there that matches the unique character of your action, which I think is interesting and perhaps an opportunity and it also shows that people aren’t necessarily taking advantage of that kind of luck the way they should be.

**Naval:** I think also at that point, it starts becoming so deterministic that it stops being luck. So, the definition starts fading from luck to more destiny. So, I would characterize that fourth one as you build your character in a certain way and then your character becomes your destiny.

**Build your character so opportunity finds you**

One of the things I think that is important to making money, when you want the kind of reputation that makes people do deals through you. I use the example of like, if you’re a great diver then treasure hunters will come and give you a piece of the treasure for your diving skills.

If you’re a trusted, reliable, high-integrity, long-term thinking deal maker, then when other people want to do deals but they don’t know how to do them in a trustworthy manner with strangers, they will literally approach you and give you a cut of the deal or offer you a unique deal just because of the integrity and reputation that you have built up.

Warren Buffett, he gets offered deals, and he gets to buy companies, and he gets to buy warrants, and bailout banks and do things that other people can’t do because of his reputation.

But of course that’s fragile. It has accountability on the line, it has a strong brand on the line, and as we will talk about later, that comes with accountability attached.

But I would say your character, your reputation, these are things that you can build that then will let you take up advantage of opportunities that other people may characterize as lucky but you know that it wasn’t luck.

**Nivi:** You said that this fourth kind of luck is more or less a destiny. There’s a quote from that original book that was in Marc’s blog posts from Benjamin Disraeli, who I think was the former prime minister of the UK. The quote to describe this kind of luck was, “we make our fortunes and we call them fate.”

**You have to be a little eccentric to be out on the frontier by yourself**

There were a couple other interesting things about this kind of luck that were mentioned in the blog post, I think it’ll be good for the listeners to hear about is that, this fourth kind of luck can almost come out of eccentric ways that you do your things and that eccentricity is not necessarily a bad thing in this case. In fact, it’s a good thing.

**Naval:** Yeah, absolutely. Because the world is a very efficient place, so, everyone has dug through all the obvious places to dig and so to find something that’s new and novel and uncovered, it helps to be operating on a frontier.

Where right there you have to be a little eccentric to be out on the frontier by yourself, and then you have to be willing to dig deeper than other people do, deeper than seems rational just because you’re interested.

**Nivi:** Yeah, the two quotes that I’ve seen that express this kind of luck in addition to that Benjamin Disraeli one, are this one from Sam Altman where he said, “extreme people get extreme results.” I think that’s pretty nice. And then there’s this other one from Jeffrey Pfeffer, who is a professor at Stanford that, “you can’t be normal and expect abnormal returns.” I’ve always enjoyed that one too.

**Naval:** Yeah. And one quote that I like which is the exact opposite of that is, “play stupid games win stupid prizes.” A lot of people spend a lot of their time playing social games like on Twitter where you’re trying to improve your social standing and you basically win stupid social prizes which are worthless.

**Nivi:** I guess the last thing that I have from this blog post is the idea that by pursuing these kinds of luck especially the last one, basically everything but dumb luck, by pursuing them you essentially run out of unluck. So, if you just keep stirring the pot and stirring the pot, that alone you will run out of unluck.

**Naval:** Yeah, or it could just be reversion to the mean. So, then you at least neutralized luck so that it’s your own talents that come into play.

## You Won’t Get Rich Renting Out Your Time

_You can’t earn non-linearly when you’re renting out your time_

**You won’t get rich renting out your time**

**Nivi:** Next you go into more specific details on how you can actually get rich, and how you can’t get rich. The first point was about how you’re not going to get rich: “You are not going to get rich renting out your time. You must own equity, a piece of the business to gain your financial freedom.”

**Naval:** This is probably one of the absolute most important points. People seem to think that you can create wealth, and make money through work. And it’s probably not going to work. There are many reasons for that.

But the most basic is just that your inputs are very closely tied to your outputs. In almost any salaried job, even at one that’s paying a lot per hour like a lawyer, or a doctor, you’re still putting in the hours, and every hour you get paid.

So, what that means is when you’re sleeping, you’re not earning. When you’re retired, you’re not earning. When you’re on vacation, you’re not earning. And you can’t earn non-linearly.

If you look at even doctors who get rich, like really rich, it’s because they open a business. They open like a private practice. And that private practice builds a brand, and that brand attracts people. Or they build some kind of a medical device, or a procedure, or a process with an intellectual property.

So, essentially you’re working for somebody else, and that person is taking on the risk, and has the accountability, and the intellectual property, and the brand. So, they’re just not gonna pay you enough. They’re gonna pay you the bare minimum that they have to, to get you to do their job. And that can be a high bare minimum, but it’s still not gonna be true wealth where you’re retired.

**Renting out your time means you’re essentially replaceable**

And then finally you’re actually just not even creating that much original for society. Like I said, this tweetstorm should have been called “How to Create Wealth.” It’s just “How to Get Rich” was a more catchy title. But you’re not creating new things for society. You’re just doing things over and over.

And you’re essentially replaceable because you’re now doing a set role. Most set roles can be taught. If they can be taught like in a school, then eventually you’re gonna be competing with someone who’s got more recent knowledge, who’s been taught, and is coming in to replace you.

You’re much more likely to be doing a job that can be eventually replaced by a robot, or by an AI. And it doesn’t even have to be wholesale replaced over night. It can be replaced a little bit at a time. And that kind of eats into your wealth creation, and therefore your earning capability.

So, fundamentally your inputs are matched to your outputs. You are replaceable, and you’re not being creative. I just don’t think that, that is a way that you can truly make money.

**You must own equity to gain your financial freedom**

So everybody who really makes money at some point owns a piece of a product, or a business, or some kind of IP. That can be through stock options, so you can be working at a tech company. That’s a fine way to start.

But usually the real wealth is created by starting your own companies, or by even investors. They’re in an investment firm, and they’re buying equity. These are much more the routes to wealth. It doesn’t come through the hours.

**You want a career where your inputs don’t match your outputs**

You really just want a job, or a career, or a profession where your inputs don’t match your outputs. If you look at modern society, again this is later in the tweetstorm. Businesses that have high creativity and high leverage tends to be ones where you could do an hour of work, and it can have a huge effect. Or you can do 1,000 hours of work, and it can have no effect.

For example, look at software engineering. One great engineer can for example create bitcoin, and create billions of dollars worth of value. And an engineer who is working on the wrong thing, or not quite as good, or just not as creative, or thoughtful, or whatever, can work for an entire a year, and every piece of code they ship ends up not getting used. Customers don’t want it.

That is an example of a profession where the input and the outputs are highly disconnected. It’s not based on the number of hours that you put in.

Whereas on the extreme other end, if you’re a lumberjack, even the best lumberjack in the world, assuming you’re not working with tools, so the inputs and outputs are clearly connected. You’re just using an ax, or a saw. You know, the best lumberjack in the world may be like 3x better than one of the worst lumberjacks, right? It’s not gonna be a gigantic difference.

So, you want to look for professions and careers where the inputs and outputs are highly disconnected. This is another way of saying that you want to look for things that are leveraged. And by leveraged I don’t mean financial leveraged alone, like Wall Street uses, and that has a bad name. I’m just talking about tools. We’re using tools.

A computer is a tool that software engineers use. If I’m a lumberjack with bulldozers, and automatic robot axes, and saws, I’m gonna be using tools, and have more leverage than someone who is just using his bare hands, and trying to rip the trees out by the roots.

Tools and leverage are what create this disconnection between inputs and outputs. Creativity, so the higher the creativity component of a profession, the more likely it is to have disconnected inputs and outputs.

So, I think that if you’re looking at professions where your inputs and your outputs are highly connected, it’s gonna be very, very, hard to create wealth, and make wealth for yourself in that process.

## Live Below Your Means for Freedom

_People busy upgrading their lifestyles just can’t fathom this freedom_

**People living below their means have freedom**

**Nivi:** Any other big things you should avoid, other than renting out your time?

**Naval:** Yeah, there are two tweets that I put out that are related. The first one I was talking about where someone, like, how your lifestyle has to upgrade, shouldn’t get upgraded too fast. And that one basically said, people who are living far below their means enjoy a freedom that people busy upgrading their lifestyles just can’t fathom.

And I think that’s very important, just to not upgrade your lifestyle all the time. To maintain your freedom. And it just gives you freedom of operation. You basically, once you make a little bit of money, you still want to be living like your old self, so that just the worry goes away. So, don’t run out to upgrade that house, and lifestyle, and all that stuff.

**The most dangerous things are heroin and a monthly salary**

Let’s say you’re getting paid $1,000 an hour. The problem is, is that when you go into a work lifestyle like that, you don’t just suddenly go from making $20 an hour to making $1,000 an hour. That’s a progression over a long career.

And as that happens, one subtle problem is that you upgrade your lifestyle as you make more, and more money. And that upgrading of the lifestyle kind of ups what you consider to be wealth, and you stay in this wage slave trap.

So, I forget who said it, maybe it was Nassim Taleb. But he said, “The most dangerous things are heroin, and a monthly salary.” Right, because they are highly addictive. The way you want to get wealthy is you want to be poor, and working, and working, and working.

**Ideally, you’ll make your money in discrete lumps**

And this is for example how the tech industry works. Where you don’t make any money for ten years, and then suddenly at year eleven, you might have a giant payday.

Which is by the way one reason why these very high marginal tax rates for the so-called wealthy are flawed because the highest risk-taking, most creative professions you literally lose money for a decade over your life, while you take massive risk, and you bleed, and bleed, and bleed.

And then suddenly in year eleven, or year fifteen, you might have one single big payday. But then of course Uncle Sam show up, and basically say, “Hey, you know what, you just made a lot money this year. Therefore, you’re rich. Therefore, you’re evil and you’ve got to hand it all over to us.” So, it just destroys those kinds of creative risk taking professions.

But ideally you want to make your money in discrete lumps, separated over long periods of time, so that your own lifestyle does not have a chance to adapt quickly, and then you basically say, “Okay, now I’m done. Now I’m retired. Now I’m free. I’m still gonna work because you got to do something with your life, but I’m gonna work on only the things that I want, when I want.” And so you have much more creative expression, and much less about money.

## Give Society What It Doesn’t Know How to Get

_Society will pay you for creating what it wants and delivering it at scale_

**Give society what it wants, but doesn’t know how to get—at scale**

**Nivi:** You’re not gonna get rich renting out your time. But you say that, “you will get rich by giving society what it wants, but does not yet know how to get at scale.”

**Naval:** That’s right. So, essentially as we talked about before, money is IOUs from society saying, “You did something good in the past. Now here’s something that we owe you for the future.” And so society will pay you for creating things that it wants.

But society doesn’t yet know how to create those things because if it did, they wouldn’t need you. They would already be stamped out big time.

Almost everything that’s in your house, in your workplace, and on the street used to be technology at one point in time. There was a time when oil was a technology, that made J.D. Rockefeller rich. There was a time when cars were technology, that made Henry Ford rich.

So, technology is just the set of things, as Alan Kay said, that don’t quite work yet \[correction: Danny Hillis\]. Once something works, it’s no longer technology. So, society always wants new things.

**Figure out what product you can provide and then figure out how to scale it**

And if you want to be wealthy, you want to figure out which one of those things you can provide for society, that it does not yet know how to get, but it will want, that’s natural to you, and within your skillset, within your capabilities.

And then you have to figure out how to scale it. Because if you just build one of it, that’s not enough. You’ve got to build thousands, or hundreds of thousands, or millions, or billions of them. So, everybody can have one.

Steve Jobs, and his team of course figured out that society would want smartphones. A computer in their pocket that had all the phone capability times 100, and be easy to use. So, they figured out how to build that, and then they figured out how to scale it.

And they figured out how to get one into every First World citizen’s pocket, and eventually every Third World citizen too. And so because of that they’re handsomely rewarded, and Apple is the most valuable company in the world.

**Nivi:** The way I tried to put it was that the entrepreneur’s job is to try to bring the high end to the mass market.

**Naval:** It starts as high end. First it starts as an act of creativity. First you create it just because you want it. You want it, and you know how to build it, and you need it. And so you build it for yourself. Then you figure out how to get it to other people. And then for a little while rich people have it.

Like, for example rich people had chauffeurs, and then they had black town cars. And then Uber came along, and everyone’s private driver is available to everybody. And now you can even see Uber pools that are replacing shuttle buses because it’s more convenient. And then you get scooters, which are even further down market of that. So, you’re right. It’s about distributing what rich people used to have to everybody.

But the entrepreneur’s job starts even before that, which is creation. Entrepreneurship is essentially an act of creating something new from scratch. Predicting that society will want it, and then figuring out how to scale it, and get it to everybody in a profitable way, in a self-sustaining way.

## The Internet Has Massively Broadened Career Possibilities

_The Internet allows you to scale any niche obsession_

**The Internet has massively broadened the possible space of careers**

**Nivi:** Let’s look at this next tweet, which I thought was cryptic, and also super interesting, about the kind of job or career that you might have. You said, “The internet has massively broadened the possible space of careers. Most people haven’t figured this out yet.”

**Naval:** The fundamental property of the internet more than any other single thing is it connects every human to each other human on the planet. You can now reach everyone.

Whether it’s by emailing them personally, whether it’s by broadcasting to them on Twitter, whether it’s by posting something on Facebook that they find, whether it’s by putting up a website they come and access.

It connects everyone to everyone. So, the internet is an inter-networking tool. It connects everybody. That is its superpower. So, you want to use that.

What that helps you figure out is the internet means you can find your audience for your product, or your talent, and skill no matter how far away they are.

For example, Nenad, who is [Illacertus](https://www.youtube.com/channel/UCmvhCWvHk3-SJqljh5cCm8A), if you look at his videos pre-internet, how would he get the message out there? It would just be … what would he do? He would run around where he lives in his neighborhood showing it to people on a computer, or a screen? Or he would try to get it played at his local movie theater? It was impossible. It only works because he can put it on the internet.

And then how many people in the world are really interested in it? Or even in interested in what we’re talking about are really gonna absorb it, right? It’s gonna be a very small subset of humanity. The key is being able to reach them.

**The Internet allows you to scale any niche obsession**

So, what the internet does is allows any niche obsession, which could be just the weirdest thing. It could be like people who collect snakes, to like people who like to ride hot air balloons, to people who like to sail around the world by themselves, just one person on a craft, or someone who’s obsessed with miniature cooking. Like, there’s this whole Japanese miniature cooking phenomenon. Or there’s a show about a woman who goes in people’s houses, and tidies it up, right?

So, whatever niche obsession you have, the internet allows you to scale. Now that’s not to say that what you build will be the next Facebook, or reach billions of users, but if you just want to reach 50,000 passionate people like you, there’s an audience out there for you.

So the beauty of this is that we have 7 billion human beings on the planet. The combinatorics of human DNA are incredible. Everyone is completely different. You’ll never meet any two people who are even vaguely similar to each other, that can substitute for each other.

It’s not like you can say, “Well, Nivi, just left my life. So, I can have this other person come in, and he’s just like Nivi. And I get the same feelings, and the same responses, and the same ideas.” No. There are no substitutes for people. People are completely unique.

So, given that each person has different skillsets, different interests, different obsessions. And it’s that diversity that becomes a creative superpower. So, each person can be creatively superb at their own unique thing.

But before that didn’t matter. Because if you were living in a little fishing village in Italy, like your fishing village didn’t necessarily need your completely unique skill, and you had to conform to just the few jobs that were available. But now today you can be completely unique.

You can go out on the internet, and you can find your audience. And you can build a business, and create a product, and build wealth, and make people happy just uniquely expressing yourself through the internet.

The space of careers has been so broadened. E-sports players, you know, people making millions of dollars playing Fortnite. People creating videos, and uploading them. YouTube broadcasters. Bloggers, podcasters. Joe Rogan, I read, true or false, I don’t know, but I read that he’s gonna make about $100 million a year on his podcast. And he’s had 2 billion downloads.

Even PewDiePie… there’s a hilarious tweet that I retweeted the other day. PewDiePie is the number one trusted name in news. This is a kid I think in Sweden, and he’s got three times the distribution of the top cable news networks. Just on his news channel. It’s not even on his entertainment channel.

**Escape competition through authenticity**

The internet enables any niche interest, as long as you’re the best at it to scale out. And the great news is because every human is different, everyone is the best at something. Being themselves.

Another tweet I had that is worth kind of weaving in, but didn’t go into this tweetstorm, was a very simple one. I like things when I can compress them down because they’re easy to remember, and easy to hook onto. But that one was, “Escape competition through authenticity.”

Basically, when you’re competing with people it’s because you’re copying them. It’s because you’re trying to do the same thing. But every human is different. Don’t copy.

I know we’re mimetic creatures, and René Girard has a whole mimesis theory. But it’s much easier than that. Don’t imitate. Don’t copy. Just do your own thing. No one can compete with you on being you. It’s that simple.

And so the more authentic you are to who you are, and what you love to do, the less competition you’re gonna have. So, you can escape competition through authenticity when you realize that no one can compete with you on being you. And normally that would have been useless advice pre-internet. Post-internet you can turn that into a career.

## Play Long-Term Games With Long-Term People

_All returns in life come from compound interest in long-term games_

**Play long-term games with long-term people**

**Nivi:** Talk a little bit about what industries you should think about working in. What kind of job you should have? And who you might want to work with? So, you said, “One should pick an industry where you can play long-term games with long-term people.” Why?

**Naval:** Yeah, this is an insight into what makes Silicon Valley work, and what makes high trust societies work. Essentially, all the benefits in life come from compound interests. Whether it’s in relationships, or making money, or in learning.

So, compound interest is a marvelous force, where if you start out with 1x what you have, and then if you increase 20% a year for 30 years, it’s not that you got 30 years times 20% added on. It was compounding, so it just grew, and grew, and grew until you suddenly got a massive amount of whatever it is. Whether it’s goodwill, or love, or relationships, or money. So, I think compound interest is a very important force.

You have to be able to play a long-term game. And long-term games are good not just for compound interest, they’re also good for trust. If you look at prisoner’s dilemma type games, a solution to prisoner’s dilemma is tit-for-tat, which is I’m just going do to you what you did last time to me, with some forgiveness in case there was a mistake made. But that only works in an iterated prisoner’s dilemma, in another words if we play a game multiple times.

So, if you’re in a situation, like for example you’re in Silicon Valley, where people are doing business with each other, and they know each other, they trust each other. Then they do right by each other because they know this person will be around for the next game.

Now of course that doesn’t always work because you can make so much money in one move in Silicon Valley, sometimes people betray each other because they’re just like, “I’m going to get rich enough off this that I don’t care.” So, there can be exceptions to all these circumstances.

But essentially if you want to be successful, you have to work with other people. And you have to figure out who can you trust, and who can you trust over a long, long period of time, that you can just keep playing the game with them, so that compound interest, and high trust will make it easier to play the game, and will let you collect the major rewards, which are usually at the end of the cycle.

So, for example, Warren Buffett has done really well as an investor in the U.S. stock market, but the biggest reason he could do that was because the U.S. stock market has been stable, and around, and didn’t get for example seized by the government during a bad administration. Or the U.S. didn’t plunge into some war. The underlying platform didn’t get destroyed. So, in his case, he was playing a longterm game. And the trust came from the U.S. stock market’s stability.

**When you switch industries, you’re starting over from scratch**

In Silicon Valley, the trust comes from the network of people in the small geographic area, that you figure out over time who you can work with, and who you can’t.

If you keep switching locations, you keep switching groups… let’s say you started out in the woodworking industry, and you built up a network there. And you’re working hard, you’re trying to build a product in the woodworking industry. And then suddenly another industry comes along that’s adjacent but different, but you don’t really know anybody in it, and you want to dive in, and make money there.

If you keep hopping from industry to … “No, actually I need to open a line of electric car stations for electric car refueling.” That might make sense. That might be the best opportunity. But every time you reset, every time you wander out of where you built your network, you’re going to be starting from scratch. You’re not going to know who to trust. They’re not going to know to trust you.

There are also industries in which people are transient by definition. They’re always coming in and going out. Politics is an example of that, right? In politics new people are being elected. You see in politics that when you have a lot of old-timers, like the Senate, people who have been around for a long time, and they’ve been career politicians.

There’s a lot of downside to career politicians like corruption. But an upside is they actually get deals done with each other because they know the other person is going to be in the same position ten years from now, and they’re going to have to keep dealing with them, so they might as well learn how to cooperate.

Whereas every time you get a new incoming freshman class in the House of Representatives, which turns over every two years with a big wave election. Nothing gets done because of a lot fighting. “Because I just got here, I don’t know you, I don’t know if you’re going to be around, why should I work with you rather than just try to do whatever I think is right?”

So, it’s important to pick an industry where you can play long-term games, and with long-term people. So, those people have to signal that they’re going to be around for a long time. That they’re ethical. And their ethics are visible through their actions.

**Long-term players make each other rich**

**Nivi:** In a long-term game, it seems that everybody is making each other rich. And in a short-term game, it seems like everybody is making themselves rich.

**Naval:** I think that is a brilliant formulation. In a longterm game, it’s positive sum. We’re all baking the pie together. We’re trying to make it as big as possible. And in a short term game, we’re cutting up the pie.

Now this is not to excuse the socialists, right? The socialists are the people who are not involved in baking the pie, who show up at the end, and say, “I want a slice, or I want the whole pie.” They show up with the guns.

But I think a good leader doesn’t take credit. A good leader basically tries to inspire people, so the team gets the job done. And then things get divided up according to fairness, and who contributed how much, or as close to it as possible, and took a risk, as opposed to just whoever has the longest knife… the sharpest knife at the end.

**Returns come from compound interest in iterated games**

**Nivi:** So, these next two tweets are, “Play iterated games. All returns in life, whether in wealth, relationships, or knowledge come from compound interest.”

**Naval:** When you have been doing business with somebody, you’ve been friends with somebody for ten years, twenty years, thirty years, it just gets better and better because you trust them so easily. The friction goes down, you can do bigger, and bigger things together.

For example, the simplest one is getting married to someone, and having kids, and raising children. That’s compound interest, right? Investing in those relationships. Those relationships end up being invaluable compared to more casual relationships.

It’s true in health and fitness. You know, the fitter you are, the easier it is to stay fit. Whereas the more you deteriorate your body, the harder it is to come back, and claw your way back to a baseline. It requires heroic acts.

**Nivi:** Regarding compound interest, I think I saw retweet something a while back. Maybe it was from Ed Latimore. It went something along the lines of, “Get some traction. Get purchase, and don’t lose it” \[correction: the tweet is by @[mmay3r](https://twitter.com/mmay3r/status/932005444179992576)\]. So, the idea was to gain some initial traction, and never fall back, just keep ratcheting up, and up.

**Naval:** I don’t remember it exactly. But I think that was right. Yes, it was like, “Get traction, and don’t let go.” It was a good one, yes.

## Pick Partners With Intelligence, Energy and Integrity

_You can’t compromise on any of these three_

**Pick business partners with high intelligence, energy and integrity**

**Naval:** In terms of picking people to work with, pick ones that have high intelligence, high energy, and high integrity, I find that’s the three-part checklist that you cannot compromise on.

You need someone who is smart, or they’ll head in the wrong direction. And you’re not going to end up in the right place. You need someone high-energy because the world is full of smart, lazy people.

We all know people in our life who are really smart, but can’t get out of bed, or lift a finger. And we also know people who are very high energy, but not that smart. So, they work hard, but they’re sort of running in the wrong direction.

And smart is not a pejorative. It’s not meant to say someone is smart, someone else is stupid. But it’s more that everyone is smart at different things. So, depending on what you want to do well, you have to find someone who is smart at that thing.

And then energy, a lot of times people are unmotivated for a specific thing, but they’re motivated for other things. So, for example, someone might be really unmotivated to go to a job, and sit in an office. But they might be really motivated to go paint, right?

Well, in that case they should be a painter. They should be putting art up on the internet. Trying to figure out how to build a career out of that, rather than wearing a collar around their neck, and going to a dreary job.

And then high integrity is the most important because otherwise if you’ve got the other two, what you have is you have a smart and hard working crook, who’s eventually going to cheat you. So, you have to figure out if the person is high-integrity.

And as we talked about, the way you do that is through signals. And signals is what they do, not what they say. It’s all the non-verbal stuff that they do when they think nobody is looking.

**Motivation has to come intrinsically**

**Nivi:** With respect to the energy, there was this interesting thing from Sam Altman a while back, where he was talking about delegation, and he was saying, “One of the important things for delegation is, delegate to people who are actually good at the thing that you want them to do.”

It’s the most obvious thing, but it seems like…  you want to partner with people who are naturally going to do the things that you want them to do.

**Naval:** Yeah. I almost won’t start a company, or hire a person, or work with somebody if I just don’t think they’re into what I want them to do.

When I was younger, I used to try and talk people into things. I had this idea that you could sell someone into doing something. But you can’t. You can’t keep them motivated. You can get them inspired initially. It might work if you’re a king like Henry V, and you’re trying to get them to just charge into battle, and then they’ll figure it out.

But if you’re trying to keep someone motivated for the long-term, that motivation has to come intrinsically. You can’t just create it, nor can you be the crutch for them if they don’t have that intrinsic motivation. So, you have to make sure people actually are high-energy, and want to do what you want them to do, and what you want to work with them on.

**Integrity is what someone does, despite what they say they do**

Reading signals is very, very important. Signals are what people do despite what they say. So, it’s important to pay attention to subtle signals. We all know that socially if someone treats a waiter, or waitress in a restaurant really badly, then it’s only a matter of time until they treat you badly.

If somebody screws over an enemy, and is vindictive towards them, well it’s only a matter of time before they redefine you from friend to enemy, and you feel their wrath. So, angry, outraged, vindictive, short-term thinking people are essentially that way in many interactions in real life.

People are oddly consistent. That’s one of the things you learn about them. So, you want to find long-term people. You want to find people who seem irrationally ethical.

For example, I had a friend of mine whose company I invested in, and the company failed, and he could have wiped out all of the investors. But he kept putting more and more personal money in. Through three different pivots he put personal money in until the company finally succeeded. And in the process, he never wiped out the investors.

And I was always grateful to him for that. I said, “Wow, that’s amazing that you were so good to your investors. You didn’t wipe them out.” And he got offended by that. He said, “I didn’t do it for you. I didn’t do it for my investors. I did it for me. It’s my own self-esteem. It’s what I care about. That’s how I live my life.” That’s the kind of person you want to work with.

Another quote that I like, I have a tweet on this. I think I read this somewhere else, so I’m not taking credit for this. But I kind of modified it a little bit. Which is that “self-esteem is the reputation that you have with yourself.” You’ll always know.

So, good people, moral people, ethical people, easy to work with people, reliable people, tend to have very high self-esteem because they have very good reputations with themselves, and they understand that.

It’s not ego. Self-esteem and ego are different things. Because ego can be undeserved, but self-esteem at least you feel like you lived up to your own internal moral code of ethics.

And so it’s very hard to work with people who end up being low integrity. And it’s hard to figure out who is high integrity and low integrity. Generally, the more someone is saying that they’re moral, ethical, and high integrity, the less likely they are to be that way.

It’s very much like status signalling. If you overtly bid for status, if you overtly talk about being high status, that is a low status move. If you openly talk about how honest, reliable, and trustworthy you are, you’re probably not that honest and trustworthy. That is a characteristic of con men.

So, yeah, pick an industry in which you can play long-term games with long-term people.

## Partner With Rational Optimists

_Don’t partner with cynics and pessimists; their beliefs are self-fulfilling_

**Don’t partner with pessimists**

**Nivi:** Let’s do this last tweet. You said, “Don’t partner with cynics, and pessimists. Their beliefs are self-fulfilling.”

**Naval:** Yes. Essentially, to create things, you have to be a rational optimist. Rational in the sense that you have to see the world for what it really is. And yet you have to be optimistic about your own capabilities, and your capability to get things done.

We all know people who are consistently pessimistic, who will shoot down everything. Everyone in their life has the helpful critical guy, right? He thinks he’s being helpful, but he’s actually being critical, and he’s a downer on everything.

That person will not only never do anything great in their lives, they’ll prevent other people around them from doing something great. They think their job is to shoot holes in things. And it’s okay to shoot holes in things as long as you come up with a solution.

There’s also the classic military line, “Either lead, follow, or get out of the way.” And these people want a fourth option, where they don’t want to lead, they don’t want to follow, but they don’t want to get out of the way. They want to tell you why the thing is not going to work.

And all the really successful people I know have a very strong action bias. They just do things. The easiest way to figure out if something is viable or not is by doing it. At least do the first step, and the second step, and the third, and then decide.

So, if you want to be successful in life, creating wealth, or having good relationships, or being fit, or even being happy, you need to have an action bias towards getting what you want.

**Partner with rational optimists**

And you have to be optimistic about it. Not irrationally. You know, there’s nothing worse than someone who is foolhardy and chasing something that’s not worth it.

That’s why I say rational optimist. But you have to be rational. Know all the pitfalls. Know the downsides, but still keep your chin up.

You’ve got one life on this planet. Why not try to build something big? This is the beauty of Elon Musk, and why I think he inspires so many people, it’s just because he takes on really, really big audacious tasks. And he provides an example for people to think big.

And it takes a lot of work to build even small things. I don’t think the corner grocery store owner is working any less hard than Elon Musk, or pouring any less sweat and toil into it. Maybe even more.

But for whatever reason, education, circumstance, they didn’t get the chance to think as big, so the outcome is not as big. So, it’s just better to think big. Obviously, rationally, within your means, stay optimistic.

The cynics and the pessimists, what they’re really saying, it’s unfortunate, but they’re basically saying, “I’ve given up. I don’t think I can do anything. And so the world to me just looks like a world where nobody can do anything. And so why should you go do something because if you fail, then I’m right, which is great. But if you succeed, then you just make me look bad.”

**We descended from pessimists**

**Nivi:** Yes, it’s probably better to be an irrational optimist, then it is to be a rational cynic.

**Naval:** There’s a completely rational frame on why you should be an optimist. Historically, if you go back 2,000 years, 5,000 years, 10,000 years, two people are wandering through a jungle, they hear a tiger. One’s an optimist, and says, “Oh, it’s not headed our way.” The other one says, “I’m a pessimist, I’m out of here.” And the pessimist runs and survives, and the optimist gets eaten.

So, we’re descended from pessimists. We’re genetically hardwired to be pessimists. But modern society is far, far safer. There are no tigers wandering around the street. It’s very unlikely that you will end up in total ruin, although you should avoid total ruin.

Much more likely that the upside is unlimited, and the downside is limited. So, adapting for modern society means overriding your pessimism, and taking slightly irrationally optimistic bets because the upside is unlimited if you start the next SpaceX, or Tesla, or Uber, you can make billions of dollars of value for society, and for yourself, and change the world.

And if you fail, what’s the big deal? You lost a few million dollars of investor money, and they’ve got plenty more, and that’s the bet they take on the chances that you will succeed.

It made sense to be pessimistic in the past. It makes sense to be optimistic today, especially if you’re educated and living in a First World country. Even a Third World country. I actually think the economic opportunities in Third World countries are much larger.

The one thing you have to avoid is the risk of ruin. Ruin means stay out of jail. So, don’t do anything that’s illegal. It’s never worth it to wear an orange jumpsuit. And stay out of total catastrophic loss. That could mean that you stay out of things that could be physically dangerous, hurt your body.

You have to watch your health. And stay out of things that can cause you to lose all of your capital, all of your savings. So, don’t gamble everything on one go. But take rationally optimistic bets with big upside.

**BOCTAOE**

**Nivi:** I think there’s people that will try and build up your ideas, and build on your ideas, no matter how far fetched they might seem. And then there are people who list all of the obvious exceptions, no matter how obvious they are.

And fortunately in the startup world, I don’t even really get exposed to the people that are giving you the obvious exceptions, and all the reasons it’s not going to work. I barely get exposed to that anymore.

**Naval:** That’s what Twitter is for. Scott Adams got so annoyed by this that he came up with a phrase, an acronym, which is “but of course there are obvious exceptions”, BOCTAOE. And he used to pin that acronym at the end of his articles for a while.

But Twitter is overrun with nitpickers. Whereas exactly as you were pointing out, Silicon Valley has learned that the upside is so great that you never look down on the kid who’s wearing a hoodie and has coffee on his shoes. And just looks like a slob because you don’t know if he’s going to be the next Mark Zuckerberg, or the next Reid Hoffman.

So, you’ve got to treat everybody with respect. You’ve got to look up to every possibility, and opportunity because the upside is so unlimited, and the downside is so limited in the modern world, especially with financial assets and instruments.

## Arm Yourself With Specific Knowledge

_Specific knowledge can be found by pursuing your genuine curiosity_

**Arm yourself with specific knowledge**

**Nivi:** Do you want to talk a little bit about the skills that you need, in particular specific knowledge, accountability, leverage and judgment. So, the first tweet in this area is “Arm yourself with specific knowledge accountability and leverage.” And I’ll throw in judgment as well. I don’t think you covered that in that particular tweet.

**Naval:** If you want to make money you have to get paid at scale. And why you, that’s accountability, at scale, that’s leverage, and just you getting paid as opposed to somebody else getting paid , that’s specific knowledge.

So, specific knowledge is probably the hardest thing to get across in this whole tweetstorm, and it’s probably the thing that people get the most confused about.

The thing is that we have this idea that everything can be taught, everything can be taught in school. And it’s not true that everything can be taught. In fact, the most interesting things cannot be taught. But everything can be learned. And very often that learning either comes from some innate characteristics in your DNA, or it could be through your childhood where you learn soft skills which are very, very hard to teach later on in life, or it’s something that is brand new so nobody else knows how to do it either, or it’s true on the job training because you’re pattern matching into highly complex environments, basically building judgment in a specific domain.

Classic example is investing, but it could be in anything. It could be in judgment in running a fleet of trucks, it could be judgment in weather forecasting.

So, specific knowledge is the knowledge that you care about. Especially if you’re later in life, let’s say your post 20, 21, 22, you almost don’t get to choose which specific knowledge you have. Rather, you get to look at what you have already built by that point in time, and then you can build on top of it.

**Specific knowledge can’t be trained**

The first thing to notice about specific knowledge is that you can’t be trained for it. If you can be trained for it, if you can go to a class and learn specific knowledge, then somebody else can be trained for it too, and then we can mass-produce and mass-train people. Heck, we can even program computers to do it and eventually we can program robots to walk around doing it.

So, if that’s the case, then you’re extremely replaceable and all we have to pay you is the minimum wage that we have to pay you to get you to do it when there are lots of other takers who can be trained to do it. So really, your returns just devolve into your cost of training plus the return on investment on that training.

So, you really want to pick up specific knowledge, you need your schooling, you need your training to be able to capitalize on the best specific knowledge, but the part of it that you’re going to get paid for is the specific knowledge.

**Specific knowledge is found by pursuing your curiosity**

For example, someone who goes and gets a degree in psychology and then becomes a salesperson. Well if they were already a formidable salesperson, a high grade salesmanship to begin with, then the psychology degree is leverage, it arms them and they do much better at sales.

But if they were always an introvert never very good at sales and they’re trying to use psychology to learn sales, they’re just not going to get that great at it.

So, specific knowledge is found much more by pursuing your innate talents, your genuine curiosity, and your passion. It’s not by going to school for whatever is the hottest job, it’s not for going into whatever field investors say is the hottest.

Very often specific knowledge is at the edge of knowledge. It’s also stuff that’s just being figured out or is really hard to figure out.

So, if you’re not 100% into it somebody else who is 100% into it will outperform you. And they won’t just outperform you by a little bit, they’ll outperform you by a lot because now we’re operating the domain of ideas, compound interest really applies and leverage really applies.

So, if you’re operating with 1,000 times leverage and somebody is right 80% of the time, and somebody else is right 90% of time, the person who’s right 90% of the time will literally get paid hundreds of times more by the market because of the leverage and because of the compounding factors and being correct. So, you really want to make sure you’re good at it so that genuine curiosity is very important.

**Building specific knowledge will feel like play to you**

So, very often, it’s not something you sit down and then you reason about, it’s more found by observation. You almost have to look back on your own life and see what you’re actually good at.

For example, I wanted to be a scientist and that is where a lot of my moral hierarchy comes from. I view scientists sort of at the top of the production chain for humanity. And the group of scientists who have made real breakthroughs and contributions that probably added more to human society, I think, than any single other class of human beings.

Not to take away anything from art or politics or engineering or business, but without the science we’d still be scrambling in the dirt fighting with sticks and trying to start fires.

My whole value system was built around scientists and I wanted to be a great scientist. But when I actually look back at what I was uniquely good at and what I ended up spending my time doing, it was more around making money, tinkering with technology, and selling people on things. Explaining things, talking to people.

So, I have some sales skills, which is a form specific knowledge that I have. I have some analytical skills around how to make money. And I have this ability to absorb data, obsess about it, and break it down and that is a specific skill that I have. I also just love tinkering with technology. And all of this stuff feels like play to me, but it looks like work to others.

So, there are other people to whom these things would be hard and they say like, “Well, how do I get good at being pithy and selling ideas?” Well, if you’re not already good at it or if you’re not really into it, maybe it’s not your thing, focus on the thing that you are really into.

This is ironic, but the first person to actually point out my real specific knowledge was my mother. She did it as an aside, talking from the kitchen and she said it when I was like 15 or 16 years old. I was telling a friend of mine that I want to be an astrophysicist and she said, “No, you’re going to go into business.”

I was like, “What, my mom’s telling me I’m going to be in business. I’m going to be an astrophysicist. Mom doesn’t know she’s talking about.” But mom knew exactly what she was talking about.

She’d already observed that every time we walk down the street, I would critique the local pizza parlor on why they were selling their slices a certain way with certain toppings and why their process of ordering was this way when it should have been that way.

So, she knew that I had more of a business curious mind, but then my obsession with science combined to create technology and technology businesses where I found myself.

So, very often, your specific knowledge is observed and often observed by other people who know you well and revealed in situations rather than something that you come up with.

## Specific Knowledge Is Highly Creative or Technical

_Specific knowledge is on the bleeding edge of technology, art and communication_

**Specific knowledge can be taught through apprenticeships**

**Naval:** To the extent that specific knowledge is taught, it’s on the job. It’s through apprenticeships. And that’s why the best businesses, the best careers are the apprenticeship or self-taught careers, because those are things society still has not figured out how to train and automate yet.

The classic line here is that Warren Buffett went to Benjamin Graham when he got out of school. Benjamin Graham was the author of the Intelligent Investor and sort of modernized or created value investing as a discipline. And Warren Buffett went to Benjamin Graham and offered to work for him for free.

And Graham said, “Actually, you’re overpriced, free is overpriced.” And Graham was absolutely right. When it comes to a very valuable apprenticeship like the type that Graham was going to give Buffet, Buffet should have been paying him a lot of money. That right there tells you that those are skills worth having.

**Specific knowledge is often highly creative or technical**

Specific knowledge also tends to be technical and creative. It’s on the bleeding edge of technology, on the bleeding edge of art, on the bleeding edge of communication.

Even today, for example, there are probably meme lords out there on the Internet who can create incredible memes that will spread the idea to millions of people. Or are very persuasive – Scott Adams is a good example of this. He is essentially becoming one of the most credible people in the world by making accurate predictions through persuasive arguments and videos.

And that is specific knowledge that he has built up over the years because he got obsessed with hypnosis when he was young, he learned how to communicate through cartooning, he embraced Periscope early, so he’s been practicing lots of conversation, he’s read all the books on the topic, he’s employed it in his everyday life. If you look at his girlfriend, she’s this beautiful young Instagram model.

That is an example of someone who has built up a specific knowledge over the course of his career. It’s highly creative, it has elements of being technical in it, and it’s something that is never going to be automated.

No one’s going to take that away from him, because he’s also accountable under one brand as Scott Adams, and he’s operating with the leverage of media with Periscope and drawing Dilbert cartoons and writing books. He has massive leverage on top of that brand and he can build wealth out of it if he wanted to build additional wealth beyond what he already has.

**Specific knowledge is specific to the individual and situation**

**Nivi:** Should we be calling it unique knowledge or does specific knowledge somehow make more sense for it?

**Naval:** You know, I came up with this framework when I was really young. We’re talking decades and decades. It’s now probably over 30 years old. So, at the time specific knowledge stuck with me so that is how I think about it.

The reason I didn’t try and change it is because every other term that I found for it was overloaded in a different way. At least specific knowledge isn’t that used. I can kind of rebrand it.

The problem with unique knowledge is, yeah, maybe it’s unique but if I learn it from somebody else it’s no longer unique, then we both know it. So, it’s not so much that it is unique, it’s that it is highly specific to the situation, it’s specific to the individual, it’s specific to the problem, and it can only be built as part of a larger obsession, interest, and time spent in that domain.

It can’t just be read straight out of a single book, nor can it be taught in a single course, nor can it be programmed into a single algorithm.

**You can’t be too deliberate about assembling specific knowledge**

**Nivi:** Speaking of Scott Adams, he’s got a blog post on how to build your career by getting in, say, the top 25 percentile at three or more things. And by doing that, you become the only person in the world who can do those three things in the 25th percentile.

So, instead of trying to be the best at one thing, you just try to be very, very good at three or more things. Is that a way of building specific knowledge?

**Naval:** I actually think the best way is just to follow your own obsession. And somewhere in the back of your mind, you can realize that, actually, this obsession I like and I’ll keep an eye out for the commercial aspects of it.

But I think if you go around trying to build it a little too deliberately, if you become too goal-oriented on the money, then you won’t pick the right thing. You won’t actually pick the thing that you love to do, so you won’t go deep enough into it.

Scott Adams’ observation is a good one, predicated on statistics. Let’s say there’s 10,000 areas that are valuable to the human race today in terms of knowledge to have, and the number one in those 10,000 slots is taken.

Someone else is likely to be the number one in each of those 10,000, unless you happen to be one of the 10,000 most obsessed people in the world that at a given thing.

But when you start combining, well, number 3,728 with top-notch sales skills and really good writing skills and someone who understands accounting and finance really well, when the need for that intersection arrives, you’ve expanded enough from 10,000 through combinatorics to millions or tens of millions. So, it just becomes much less competitive.

Also, there’s diminishing returns. So, it’s much easier to be top 5 percentile at three or four things than it is to be literally the number one at something.

**Build specific knowledge where you are a natural**

I think it’s a very pragmatic approach. But I think it’s important that one not start assembling things too deliberately because you do want to pick things where you are a natural. Everyone is a natural at something.

We’re all familiar with that phrase, a natural. “Oh, this person is a natural at meeting men or women, this person is a natural socialite, this person is a natural programmer, this person is a natural reader.” So, whatever you are a natural at, you want to double down on that.

And then there are probably multiple things you’re natural at because personalities and humans are very complex. So, we want to be able to take the things that you are natural at and combine them so that you automatically, just through sheer interest and enjoyment, end up top 25% or top 10% or top 5% at a number of things.

## Learn to Sell, Learn to Build

_If you can do both, you will be unstoppable_

**Learn to sell, learn to build**

**Nivi:** Talking about combining skills, you said that you should “learn to sell, learn to build, if you can do both, you will be unstoppable.”

**Naval:** This is a very broad category. It’s two broad categories. One is building the product. Which is hard, and it’s multivariate. It can include design, it can include development, it can include manufacturing, logistics, procurement, it can even be designing and operating a service. It has many, many definitions.

But in every industry, there is a definition of the builder. In our tech industry it’s the CTO, it’s the programmer, it’s the software engineer, hardware engineer. But even in the laundry business, it could be the person who’s building the laundry service, who is making the trains run on time, who’s making sure all the clothes end up in the right place at the right time, and so on.

The other side of it is sales. Again, selling has a very broad definition. Selling doesn’t necessarily just mean selling individual customers, but it can mean marketing, it can mean communicating, it can mean recruiting, it can mean raising money, it can mean inspiring people, it could mean doing PR. It’s a broad umbrella category.

**The Silicon Valley model is a builder and seller**

So, generally, the Silicon Valley startup model tends to work best. It’s not the only way, but it is probably the most common way, when you have two founders, one of whom is  world class at selling, and one of whom is world class at building.

Examples are, of course, Steve Jobs and Steve Wozniak with Apple, Gates and Allen probably had similar responsibilities early on with Microsoft, Larry and Sergey probably broke down along those lines, although it’s a little different there because that was a very technical product delivered to end users through a simple interface.

But generally, you will see this pattern repeated over and over. There’s a builder and there’s a seller. There’s a CEO and CTO combo. And venture and technology investors are almost trained to look for this combo whenever possible. It’s the magic combination.

**If you can do both you will be unstoppable**

The ultimate is when one individual can do both. That’s when you get true superpowers. That’s when you get people who can create entire industries.

The living example is Elon Musk. He may not necessarily be building the rockets himself, but he understands enough that he actually makes technical contributions. He understands the technology well enough that no one’s going to snow him on it, and he’s not running around making claims that he doesn’t think he can’t eventually deliver. He may be optimistic on the timelines but he thinks this is within reasonableness for delivery.

Even Steve Jobs developed enough product skills and was involved enough in the product that he also operated in both of these domains. Larry Ellison started as a programmer and I think wrote the first version of Oracle, or was actually heavily involved in it.

Marc Andreessen was also in this domain. He may not have had enough confidence in his sales skills, but he was the programmer who wrote Netscape Navigator, or a big chunk of it. So, I think the real giants in any field are the people who can both build and sell.

**I’d rather teach an engineer marketing than a marketer engineering**

And usually the building is a thing that a sales person can’t pick up later in life. It requires too much focused time. But a builder can pick up selling a little bit later, especially if they were already innately wired to be a good communicator. Bill Gates famously paraphrases this as, “I’d rather teach an engineer marketing, than a marketer engineering.”

I think if you start out with a building mentality and you have building skills and it’s still early enough in your life, or you have enough focused time that you think you can learn selling, and you have some natural characteristics or you’re a good salesperson, then you can double down on those.

Now, your sales skills could be in a different than traditional domain. For example, let’s say you’re a really good engineer and then people are saying, well, now you need to be good at sales, well, you may not be good at hand-to-hand sales, but you may be a really good writer.

And writing is a skill that can be learned much more easily than, say, in-person selling, and so you may just cultivate writing skills until you become a good online communicator and then use that for your sales.

On the other hand, it could just be that you’re a good builder and you’re bad at writing and you don’t like communicating to mass audiences but you’re good one-on-one, so then you might use your sales skills for recruiting or for fundraising, which are more one-on-one kinds of endeavors.

This is pointing out that if you’re at the intersection of these two, don’t despair because you’re not going to be the best technologist and you’re not going to be the best salesperson, but in a weird way, that combination, back to the Scott Adams skill stack, that combination of two skills is unstoppable.

Long term, people who understand the underlying product and how to build it and can sell it, these are catnip to investors, these people can break down walls if they have enough energy, and they can get almost anything done.

**Nivi:** If you could only pick one to be good at, which one would you pick?

**Naval:** When you’re trying to stand out from the noise building is actually better because there’re so many hustlers and sales people who have nothing to back them up. When you’re starting out, when you’re trying to be recognized, building is better.

But much later down the line building gets exhausting because it is a focus job and it’s hard to stay current because there’s always new people, new products coming up who have newer tools, and frankly more time because it’s very intense, it’s a very focused task.

So, sales skills actually scale better over time. Like for example, if you have a reputation for building a great product, that’s good, but when you ship your new product, I’m going to validate it based on the product. But if you have a reputation for being a good person to do business with and you’re persuasive and communicative then that reputation almost becomes self-fulfilling.

So, I think if you only had to pick up one, you can start with building and then transition to selling. This is a cop-out answer, but I think that is actually the right answer.

## Read What You Love Until You Love to Read

_You should be able to pick up any book in the library and read it_

**Read what you love until you love to read**

**Nivi:** Before we go and talk about accountability and leverage and judgment, you’ve got a few tweets further down the line that I would put in the category of continuous learning.

They’re essentially, “there is no skill called business. Avoid business magazines and business class, study microeconomics, game theory, psychology, persuasion, ethics, mathematics and computers.”

There’s one other comment that you made in a Periscope that was, “you should be able to pick up any book in the library and read it.” And the last tweet in this category was, “reading is faster than listening, doing is faster than watching.”

**Naval:** Yeah, the most important tweet on this, I don’t even have in here unfortunately, which is, the foundation of learning is reading. I don’t know a smart person who doesn’t read and read all the time.

And the problem is, what do I read? How do I read? Because for most people it’s a struggle, it’s a chore. So, the most important thing is just to learn how to educate yourself and the way to educate yourself is to develop a love for reading.

So, the tweet that is left out, the one that I was hinting at is, “read what you love until you love to read.” It’s that simple.

Everybody I know who reads a lot loves to read, and they love to read because they read books that they loved. It’s a little bit of a catch-22, but you basically want to start off just reading wherever you are and then keep building up from there until reading becomes a habit. And then eventually, you will just get bored of the simple stuff.

So you may start off reading fiction, then you might graduate to science fiction, then you may graduate to non-fiction, then you may graduate to science, or philosophy, or mathematics or whatever it is, but take your natural path and just read the things that interest you until you kind of understand them. And then you’ll naturally move to the next thing and the next thing and the next thing.

**Read the original scientific books in a field**

Now, there is an exception to this, which is where I was hinting with what things you actually do want to learn, which is, at some point there’s too much out there to read. Even reading is full of junk.

There are actually things you can read, especially early on, that will program your brain a certain way, and then later things that you read, you will decide whether those things are true or false based on the earlier things.

So, it is important that you read foundational things. And foundational things, I would say, are the original books in a given field that are very scientific in their nature.

For example, instead of reading a business book, pick up Adam Smith’s The Wealth of Nations. Instead of reading a book on biology or evolution that’s written today, I would pick up Darwin’s Origin of the Species. Instead of reading a book on biotech right now that may be very advanced, I would just pick up The Eighth Day of Creation by Watson and Crick. Instead of reading advanced books on what cosmology and what Neil Degrasse Tyson and Stephen Hawking have been saying, you can pick up Richard Feynman’s Six Easy Pieces and start with basic physics.

**Don’t fear any book**

If you understand the basics, especially in mathematics and physics and sciences, then you will not be afraid of any book. All of us have that memory of when we were sitting in class and we’re learning mathematics, and it was all logical and all made sense until at one point the class moved too fast and we fell behind.

Then after that we were left memorizing equations, memorizing concepts without being able to derive them from first principles. And at that moment, we’re lost, because unless you’re a professional mathematician, you’re not going to remember those things. All you’re going to remember are the techniques, the foundations.

So, you have to make sure that you’re building on a steel frame of understanding because you’re putting together a foundation for skyscraper, and you’re not just memorizing things because you’re just memorizing things you’re lost. So the foundations are ultra important.

And the ultimate, the ultimate is when you walk into a library and you look at it up and down and you don’t fear any book. You know that you can take any book off the shelf, you can read it, you can understand it, you can absorb what is true, you can reject what is false, and you have a basis for even working that out that is logical and scientific and not purely just based on opinions.

**The means of learning are abundant; the desire to learn is scarce**

The beauty of the internet is the entire library of Alexandria times 10 is at your fingertips at all times. It’s not the means of education or the means of learning are scarce, the means of learning are abundant. It’s the desire to learn that’s scarce. So, you really have to cultivate the desire.

And it’s not even cultivating you’ve to not lose it. Children have a natural curiosity. If you go to a young child who’s first learning language, they’re pretty much always asking: What’s this? What’s that? Why is this? Who’s that? They’re always asking questions.

But one of the problems is that schools and our educational system, and even our way of raising children replaces curiosity with compliance. And once you replace the curiosity with the compliance, you get an obedient factory worker, but you no longer get a creative thinker. And you need creativity, you need the ability to feed your own brain to learn whatever you want.

## The Foundations Are Math and Logic

_Mathematics and logic are the basis for understanding everything else_

**The ultimate foundations are math and logic**

**Naval:** Foundational things are principles, they’re algorithms, they’re deep seated logical understanding where you can defend it or attack it from any angle. And that’s why microeconomics is important because macroeconomics is a lot of memorization, a lot of macro bullshit.

As Nassim Taleb says, it is easier to macro bullshit than it is the micro bullshit. Because macroeconomics is voodoo-complex-science meets politics. You can’t find two macroeconomists to agree on anything these days, and different macroeconomists get used by different politicians to peddle their different pet theories.

There are even macroeconomists out there now peddling something called Modern Monetary Theory which basically says, hey, except for this pesky thing called inflation, we can just print all the money that we want. Yes, except for this pesky thing called inflation. That’s like saying, except for limited energy, we can fire rockets off into space all day long.

It’s just nonsense, but the fact that there are people who have “macroeconomist” in their title and are peddling Modern Monetary Theory just tells you that macroeconomics as a so-called science has been corrupted. It’s now a branch of politics.

So, you really want to focus on the foundations. The ultimate foundation are mathematics and logic. If you understand logic and mathematics, then you have the basis for understanding the scientific method. Once you understand the scientific method, then you can understand how to separate truth from falsehood in other fields and other things that you’re reading.

**It’s** **better to read a great book slowly than to fly through a hundred books quickly**

So, be very careful about reading other people’s opinions and even be careful when reading facts because so-called facts are often just opinions with a veneer \[of pseudoscience\] around them.

What you are really looking for are algorithms. What you are really looking for is understanding. It’s better to go through a book really slowly and struggle and stumble and rewind, than it is to fly through it quickly and say, “Well, now I’ve read 20 books, I’ve read 30 books, I’ve read 50 books in the field.”

It’s like Bruce Lee said, “I don’t fear the man who knows a thousand kicks and a thousand punches, I fear the man who’s practiced one punch ten thousand times or one kick ten thousand times.” It’s that understanding that comes through repetition and through usage and through logic and foundations that really makes you a smart thinker.

**Learn persuasion and programming**

**Nivi:** To lay a foundation for learning for the rest of your life I think you need two things, if I was going to try and sum it up. One, practical persuasion and two, you need to go deep in some technical category, whether it’s abstract math, or you want to read Donald Knuth’s books on algorithms, or you want to read Feynman’s lectures on physics.

If you have practical persuasion and a deep understanding of some complex topic, I think you’ll have a great foundation for learning for the rest of your life.

**Naval:** Yeah. In fact let me expand that a little bit. I would say that the five most important skills are of course, reading, writing, arithmetic, and then as you’re adding in, persuasion, which is talking. And then finally, I would add computer programming just because it’s an applied form of arithmetic that just gets you so much leverage for free in any domain that you operate in.

If you’re good with computers, if you’re good at basic mathematics, if you’re good at writing, if you’re good at speaking, and if you like reading, you’re set for life.

## There’s No Actual Skill Called ‘Business’

_Avoid business schools and magazines_

**There’s no actual skill called ‘business’**

**Naval:** In that sense, business to me is bottom of the barrel. There’s no actual skill called business, it’s too generic. It’s like a skill called “relating.” Like “relating to humans.” That’s not a skill, it’s too broad.

A lot of what goes on in business schools, and there is some very intelligent stuff taught in business schools – I don’t mean to detract from them completely – some of the things taught in business school are just anecdotes. They call them “case studies.”

But they’re just anecdotes, and they’re trying to help you pattern match by throwing lots of data points at you, but the reality is, you will never understand them fully until you’re actually in that position yourself. 

Even then you will find that basic concepts from game theory, psychology, ethics, mathematics, computers, and logic will serve you much, much better.

I would focus on the foundations, I would focus with a science bent. I would develop a love for reading, including by reading so-called junk food that you’re not supposed to read. You don’t have to read the classics. That \[reading\] is the foundation for your self-education.

**Doing is faster than watching**

**Nivi:** What did you mean when you said that “doing is faster than watching?”

**Naval:** When it comes to your learning curve, if you want to optimize your learning curve… One of the reasons why I don’t love podcasts, even though I’m a generator of podcasts, is that I like to consume my information very quickly.

And I’m a good reader, or a fast reader and I can read very fast but I can only listen at a certain speed. I know people listen at 2x, 3x, but everyone sounds like a chipmunk and it’s hard to go back, it’s hard to highlight, it’s hard to pinpoint snippets and save them in your notebook, and so on.

Similarly, a lot of people think they can become really skilled at something by watching others do it, or even by reading about others doing it. And going back to the business school case study, that’s a classic example.

They study other people’s businesses, but in reality, you’re going to learn a lot more about running a business by operating your own lemonade stand or equivalent. Or even opening a little retail store down the street.

That is how you’re going to learn on the job because a lot of the subtleties don’t express themselves until you’re actually in the business.

For example, everyone’s into mental models these days. You go to Farnam Street, you go to Poor Charlie’s Almanack, and you can learn all the different mental models. But which ones matter more? Which ones do you apply more often? Which ones matter in which circumstances? That’s actually the hard part.

For example, my personal learning has been that the principal-agent problem drives so much in this world. It’s an incentives problem. I’ve learned that tit-for-tat iterated prisoner’s dilemma is the piece of game theory that is worth knowing the most. You can almost put down the game theory book after that.

By the way, the best way to learn game theory is to play lots of games. I never even read game theory books. I consider myself extremely good at game theory. I’ve never opened up a game theory book and found a result in there where I didn’t think, “Oh, yeah, that’s common sense to me.”

The reason is that I grew up playing all kinds of games and I ran into all kinds of corner cases with all kinds of friends, and so it’s just second nature to me. You can always learn better by doing it on the job.

**The number of ‘doing’ iterations drives the learning curve**

But doing is a subtle thing. Doing encapsulates a lot. For example, let’s say, I want to learn how to run a business. Well, if I start a business where I go in every day and I’m doing the same thing, let’s say I’m running a retail store down the street where I’m stocking the shelves with food and liquor every single day, I’m not going to learn that much because I’m repeating things a lot.

So, I’m putting in thousands of hours, but they are thousands of hours doing the same thing. Whereas if I was putting in thousands of iterations, that would be different. So, the learning curve is across iterations \[not iterations\].

So if I was trying new marketing experiments in the store all the time, I was constantly changing up the inventory, I was constantly changing up the branding and the messaging, I was constantly changing the sign, I was constantly changing the online channels that are used to drive foot traffic in, I was experimenting with being open at different hours, I had the ability to walk around and talk to other store owners and getting their books and figure out how they run their businesses.

It’s the number of iterations that drives the learning curve. So, the more iterations you can have, the more shots on goal you can have, the faster you’re going to learn. It’s not just about the hours put in.

**If you’re willing to bleed a little every day, you may win big later**

It’s actually a combination of the two, but I think just the way we’re built and the way that the world presents itself, the world offers us very easily the opportunity to do the same thing over and over and over again. But really, we’d be better served if we went off and found ways to do new things from scratch.

And doing something new the first time is painful, because you’re wandering into uncertain territory and high odds are that you will fail. So you just have to get very, very comfortable with frequent small failures.

Nassim Taleb talks about this also. He made his fortune, his wealth by being a trader who basically relied upon black swans. Nassim Taleb made money by losing little bits of money every day and then once in a blue moon he would make a lot of money when the unthinkable happened for other people.

Whereas most people want to make little bits of money every day and in exchange they’ll tolerate lots of blow-up risk, they’ll tolerate going completely bankrupt.

We’re not evolved to bleed a little bit every day. If you’re out in the natural environment, and you get a cut and you’re literally bleeding a little bit every day, you will eventually die. You’ll have to stop that cut.

We’re evolved for small victories all the time but that becomes very expensive. That’s where the crowd is. That’s where the herd is. So, if you’re willing to bleed a little bit every day but in exchange you’ll win big later, you will do better.

That is, by the way, entrepreneurship. Entrepreneurs bleed every day.

They’re not making money, they’re losing money, they’re constantly stressed out, all the responsibility is upon them, but when they win they win big. On average they’ll make more.

## Embrace Accountability to Get Leverage

_Take risks under your own name and society will reward you with leverage_

**You need accountability to get leverage**

**Nivi:** Why don’t we jump into accountability, which I thought was pretty interesting and I think you have your own unique take on it. So the first tweet on accountability was, “Embrace accountability and take business risks under your own name. Society will reward you with responsibility, equity, and leverage.”

**Naval:**  Yeah. So to get rich, you’re going to need leverage. Leverage comes in labor, comes in capital, or it can come through code or media. But most of these, like labor and capital, people have to give to you. For labor, somebody has to follow you. For capital, somebody has to give you money or assets to manage or machines.

So to get these things, you have to build up credibility and you have to do those under your own name as much as possible, which is risky. So accountability is a double-edged thing. It allows you to take credit when things go well and to bear the brunt of the failure when things go badly.

**Take business risks under your own name**

So in that sense, people who are stamping their names on things aren’t foolish. They’re just confident. Maybe it turns out to be foolish in the end, but if you look at a Kanye or an Oprah or a Trump or an Elon or anyone like that, these people can get rich just off their name because their name is such powerful branding.

Regardless of what you think of Trump, you have to realize that the guy was among the best in the world at just branding his name. Why would you go to Trump Casino? Used to be because Trump. Why would you go to a Trump tower? Because of Trump.

When it came time to vote, I think that a lot of voters just went in and said, “Trump.” They recognize the name, so the name recognition paid off.

Same thing with Oprah. She puts her brand on something, her name on something and it flies off the shelves, and it’s like an instant validator.

These people also take risks for putting their name out there. Obviously Trump is now probably hated by half or more than half of the country and by a big chunk of the world as he sticks his name out there.

By putting your name out there, you become a celebrity, and fame has many, many downsides. It’s better to be anonymous and rich than to be poor and famous, but even famous and rich has a lot of downsides associated with it. You’re always in the public eye.

**A well-functioning team has clear accountability for each position**

Accountability is quite important, and when you’re working to build a product or you’re working in a team or you’re working in a business, we constantly have drummed into our heads how important it is to be part of a team. Absolutely agree with that.

A lot of our training socially is telling us to not stick our necks out of the crowd. There’s a saying that I hear from our Australian friends that the tall poppy gets cut. Don’t stick your neck out, but I would say that actually a really, really well-functioning team is small and has clear accountability for each of the different portions.

You can say, “Okay, this person’s responsible for building the product. This person’s responsible for the messaging. This person’s responsible for raising money. This person’s responsible for the pricing strategy and maybe the online advertising.” So if somebody screws up, you know exactly who’s responsible. While at the same time if something goes really well, you also know exactly who’s responsible.

If you have a small team and you have clearly delineated responsibilities, then you can still keep a very high level of accountability. Accountability is really important because when something succeeds or fails, if it fails, everybody points fingers at each other, and if it succeeds, everybody steps forward to take credit.

We’ve all had that experience when we were in school and we got a group assignment to do. There were probably a few people in there who did a lot of the work. Then there are a few people who just did a lot of grandstanding or positioning to do the work. We’re all familiar with this from a childhood sense, but it’s sort of uncomfortable to talk about.

**People who can fail in public have a lot of power**

Clear accountability is important. Without accountability, you don’t have incentives. Without accountability, you can’t build credibility. But you take risk. You take risk of failure. You take risk of humiliation. You take risk of failure under your own name.

Luckily in modern society, there’s no more debtors’ prison and people don’t go to jail or get executed for losing other people’s money, but we’re still socially hard wired to not fail in public under our own names. The people who have the ability to fail in public under their own names actually gain a lot of power .

For example, I’ll give a personal anecdote. Up until about 2013, 2014, my public persona was an entirely around startups and investing. Only around 2014, 2015 did I start talking about philosophy and psychological things and broader things.

It made me a little nervous because I was doing it under my own name. There were definitely people in the industry who sent me messages through the back channel like, “What are you doing? You’re ending your career. This is stupid.”

I kind of just went with it. I took a risk. Same with crypto. Early on, I took a risk.

But when you put your name out there, you take a risk with certain things. You also get to reap the rewards. You get the benefits.

## Take Accountability to Earn Equity

_If you have high accountability, you’re less replaceable_

**Accountability is how you’re going to get equity**

**Naval:** Accountability is important because that’s how you’re going to get leverage. That’s how you’re going to get credibility. It’s also how you’re going to get equity. You’re going to get a piece of the business.

When you’re negotiating with other people, ultimately if someone else is making a decision about how to compensate you, that decision will be based on how replaceable you are. If you have high accountability, that makes you less replaceable. Then they have to give you equity, which is a piece of the upside.

**Taking accountability is like taking equity in all your work**

Equity itself is a good example because equity is also a risk-based instrument. Equity means you get paid everything after all the people who need guaranteed money are paid back.

If you look at the hierarchy of capital in a company, the employees get paid first. They get paid the salary first. In legal \[bankruptcy\] proceedings, the salaries are sacrosanct. If you’re a board member and the company spends too much money and has back salaries to pay, the government can go after you personally to pay back the salaries. The employees get the most security, but in exchange for that security, they don’t have as much upside.

Next in line would be the debt holders who are maybe the bankers who lend money to the company for operations and they need to make their fixed coupon every month or every year, but they don’t get much more upside beyond that. They might be making 5, 10, 15, 20, 25% a year, but that’s what their upside is limited to.

Finally there are the equity holders. These people are actually going to get most of the upside. Once the debt holders are paid off and the salaries are paid off, whatever remains goes to them.

But if there isn’t enough money to pay off the salaries and the debt holders, or if there’s just barely enough to pay off the salary and the debt holders, which is what happens with most businesses, most of the times, the equity holders get nothing.

The equity holders take on greater risk, but in exchange, they get nearly unlimited upside. You can do the same with all of your work. Essentially, taking accountability for your actions is the same as taking an equity position in all of your work. You’re taking greater downside risk for greater upside.

Realize that in modern society, the downside risk is not that large. Even personal bankruptcy can wipe the debts clean in good ecosystems. I’m most familiar with Silicon Valley, but generally people will forgive failures as long as you were honest and made a high integrity effort.

There’s not really that much to fear in terms of failure, and so people should be taking on a lot more accountability than they actually are.

**Nivi:**  Is accountability actually fragile or do you really just mean that we’re hardwired not to fail in public, so it just feels like it’s a fragile thing?

**Naval:**  I think it could actually be fragile. An example of accountability is you’re an airplane pilot. As a captain, you’re taking on accountability for the entire plane.

Let’s say that something goes wrong with the aircraft. You can’t later blame it on anyone else. You can’t blame it on the steward or the stewardess. You can’t blame it on the copilot. You’re the captain. You’re responsible for the ship. If you screw up, you crash the ship, and there are immediate consequences.

In the old days, the captain was expected to go down with the ship. If the ship was sinking, then literally the last person who got to get off was the captain. I think accountability does come with real risks, but we’re talking about a business context.

The risk here would be that you would probably be the last one to get your capital back out. You’d be the last one to get paid for your time. The time that you’ve put in, the capital that you’ve put into the company, these are what are at risk.

Even if a business fails and your name’s on it, that’s not as bad as if it turns out to be an integrity issue. Bernie Madoff, for example, Madoff investments, that name is never going to be good again in the investment community. You could be Bernie Madoff’s great-great-great-grandson. You are not going to go into the investment business because he ruined the family name.

I think these days the accountability risk with a name happens more around integrity, rather than it does around purely economic failure.

**Accountability is reputational skin in the game**

**Nivi:**  The big takeaway for me on accountability is that you will be rewarded directly in proportion with your accountability. I also think this is why people like Taleb rail against CEOs who get rewards without accountability.

**Naval:**  Yeah. Taleb’s Skin In The Game is required reading. If you want to get anywhere in modern life and understand how modern systems work, then Skin In The Game would be near the top of my list to read.

Accountability, skin in the game, these concepts go very closely hand in hand. I think of accountability as reputational skin in the game. It’s putting your personal reputation on the line as skin in the game.

Accountability is a simple concept. The only part of accountability that may be a little counterintuitive is that we’re currently socially brainwashed to not take on accountability, not in a visible way.

I think there are ways to take on accountability where every member of a team can take on accountability for their portion. That is how you get a well-functioning team while still putting credits and losses in the correct columns.

## Labor and Capital Are Old Leverage

_Everyone is fighting over labor and capital_

**Our brains aren’t evolved to comprehend new forms of leverage**

**Nivi:** Why don’t we talk a little bit about leverage?

The first tweet in the storm was a famous quote from Archimedes, which was, “Give me a lever long enough and a place to stand and I will move the Earth.”

The next tweet was, “Fortunes require leverage. Business leverage comes from capital, people and products with no marginal costs of replication.”

**Naval:** Leverage is critical. The reason I stuck in Archimedes quote in there is… normally I don’t like putting other people’s quotes in my Twitter. That doesn’t add any value. You can go look up those people’s quotes. But this quote I had to put in there because it’s just so fundamental. I read it when I was very, very young and it had a huge impression on me.

We all know what leverage is when we use a seesaw or a lever. We understand how that works physically, but I think what our brains aren’t really well-evolved to comprehend is how much leverage is possible in modern society and what the newest forms of leverage are.

**Society overvalues labor leverage**

The oldest form of leverage is labor, which is people working for you. Instead of me lifting rocks, I can have 10 people lift rocks. Then just by my guidance on where the rock should go, a lot more rocks get moved than I could do myself. Everybody understands this because we’re evolved to understand the labor form of leverage, so what happens is society overvalues labor as a form of leverage.

This is why your parents are impressed when you get a promotion and you have lots of people working underneath you. This is why when a lot of naive people, when you tell them about your company, they’ll say, “How many people work there?” They’ll use that as a way to establish credibility. They’re trying to measure how much leverage and impact you actually have.

Or when someone starts a movement, they’ll say how many people they have or how big the army is. We just automatically assume that more people is better.

**You want the minimum amount of labor that allows you to use the other forms of leverage**

I would argue that this is the worst form of leverage that you could possibly use. Managing other people is incredibly messy. It requires tremendous leadership skills. You’re one short hop from a mutiny or getting eaten or torn apart by the mob.

It’s incredibly competed over. Entire civilizations have been destroyed over this fight. For example, communism, Marxism, is all about the battle between capital and labor, das kapital and das labor. It’s kind of a trap.

You really want to stay out of labor-based leverage. You want the minimum amount of people working with you that are going to allow you to use the other forms of leverage, which I would argue are much more interesting.

**Capital has been the dominant form of leverage in the last century**

The second type of leverage is capital. This one’s a little less hardwired into us because large amounts of money moving around and being saved and being invested in money markets, these are inventions of human beings the in last few hundred to few thousand years. They’re not evolved with us from hundreds of thousands of years.

We understand them a little bit less well. They probably require more intelligence to use correctly, and the ways in which we use them keep changing. Management skills from a hundred years ago might still apply today, but investing in the stock market skills from a hundred years ago probably don’t apply to the same level today.

Capital is a trickier form of leverage to use. It’s more modern. It’s the one that people have used to get fabulously wealthy in the last century. It’s probably been the dominant form of leverage in the last century.

You can see this by who are the richest people. It’s bankers, politicians in corrupt countries who print money, essentially people who move large amounts of money around.

If you look at the top of very large companies, outside of technology companies, in many, many large old companies, the CEO job is really a financial job. They’re really financial asset managers. Sometimes, an asset manager can put a pleasant face on it, so you get a Warren Buffet type.

But deep down, I think we all dislike capital as a form of leverage because it feels unfair. It’s this invisible thing that can be accumulated and passed across generations and suddenly seems to result in people having gargantuan amounts of money with nobody else around them or necessarily sharing in it.

That said, capital is a powerful form of leverage. It can be converted to labor. It can be converted to other things. It’s very surgical, very analytical.

If you are a brilliant investor and give $1 billion and you can make a 30% return with it, whereas anybody else can only make a 20% return, you’re going to get all the money and you’re going to get paid very handsomely for it.

It scales very, very well. If you get good at managing capital, you can manage more and more capital much more easily than you can manage more and more people.

**You need specific knowledge and accountability to obtain capital**

It is a good form of leverage, but the hard part with capital is how do you obtain it? That’s why I talked about specific knowledge and accountability first.

If you have specific knowledge in a domain and if you’re accountable and you have a good name in that domain, then people are going to give you capital as a form of leverage that you can use to then go get more capital.

Capital also is fairly well understood. I think a lot of the knocks against capitalism come because of the accumulation of capital.

## Product and Media Are New Leverage

_Create software and media that work for you while you sleep_

**Product and media are the new leverage**

**Naval:** The most interesting and the most important form of leverage is this idea of products that have no marginal cost of replication. This is the new form of leverage.

This was only invented in the last few hundred years. It got started with the printing press. It accelerated with broadcast media, and now it’s really blown up with the Internet and with coding.

Now, you can multiply your efforts without having to involve other humans and without needing money from other humans.

This podcast is a form of leverage. Long ago, I would have had to sit in a lecture hall and lecture each of you personally. I would have maybe reached a few hundred people and that would have been that.

Then 40 years ago, 30 years ago, I would have to be lucky to get on TV, which is somebody else’s leverage. They would have distorted the message. They would taken the economics out of it or charged me for it. They would have muddled the message, and I would have been lucky to get that form of leverage.

Today, thanks to the Internet, I can buy a cheap microphone, hook it up to a laptop or an iPad, and there you are all listening.

**Product leverage is where the new fortunes are made**

This newest form of leverage is where all the new fortunes are made, all the new billionaires. The last generation, fortunes were made by capital. That was the Warren Buffets of the world.

But the new generation’s fortunes are all made through code or media. Joe Rogan making 50 to a 100 million bucks a year from his podcast. You’re going to have a PewDiePie. I don’t know how much money he’s rolling in, but he’s bigger than the news. The Fortnite players. Of course Jeff Bezos and Mark Zuckerberg and Larry Page and Sergey Brin and Bill Gates and Steve Jobs. That is all code-based leverage.

**Combining all three forms of leverage is a magic combination**

Now, the beauty is when you combine all of these three. That’s where tech startups really excel, where you take just the minimum, but highest output labor that you can get, which are engineers, and designers, product developers. Then you add in capital. You use that for marketing, advertising, scaling. You add in lots of code and media and podcasts and content to get it all out there.

That is a magic combination, and that’s why you see technology startups explode out of nowhere, use massive leverage and just make huge outsize returns.

**Product and media leverage are permissionless**

**Nivi:** Do you want to talk a little bit about permissioned versus permissionless?

**Naval:** Probably the most interesting thing to keep in mind about the new forms of leverage is they are permissionless. They don’t require somebody else’s permission for you to use them or succeed.

For labor leverage, somebody has to decide to follow you. For capital leverage, somebody has to give you money to invest or to turn into a product.

Coding, writing books, recording podcasts, tweeting, YouTubing, these kinds of things, these are permissionless. You don’t need anyone’s permission to do them, and that’s why they are very egalitarian. They’re great equalizers of leverage.

As much as people may rail on Facebook and YouTube, they’re not going to stop using it because this permissionless leverage, where everyone can be a broadcaster, is just too good.

The same way you can rail upon Apple for having a slightly closed ecosystem in the iPhone, but everyone’s writing apps for it. As long as you can write apps for it, you can get rich or reach users doing that, why not?

**The robot army is already here—code lets you tell them what to do**

I think of all the forms of leverage, the best one in modern society … This is glib. This is a little overused. This is why I tell people learn to code. It’s that we have this idea that in the future there’s going to be these robots and they’re going to be doing everything.

That may be true, but I would say that the majority of the robot revolution has already happened. The robots are already here and there are way more robots than there are humans, it’s just that we pack them in data centers for heat and efficiency reasons. We put them in servers. They’re inside the computers. All the circuits, it’s robot minds inside that’s doing all the work.

Every great software developer, for example, now has an army of robots working for him at nighttime, while he or she sleeps, after they’ve written the code and it’s just cranking away.

The robot army is already here. The robot revolution has already happened. We’re about halfway through it. We’re just adding in much more of the hardware component these days as we get more comfortable with the idea of autonomous vehicles and autonomous airplanes and autonomous ships and maybe autonomous trucks. There’re delivery bots and Boston Dynamics robots and all that.

But robots who are doing web searching for you, for example, are already here. The ones who are cleaning up your video and audio and transmitting it around the world are already here. The ones who are answering many customer service queries, things that you would have had to call a human for are already here.

An army of robots is already here. It’s very cheaply available. The bottleneck is just figuring out intelligent and interesting things to do to them.

Essentially you can order this army of robots around. The commands have to be issued in a computer language, in a language that they understand.

These robots aren’t very smart. They have to be told very precisely what to do and how to do it. Coding is such a great superpower because now you can speak the language of the robot armies and you can tell them what to do.

**Nivi:** I think at this point, people are not only commanding the army of robots within servers through code, they’re actually manipulating the movement of trucks, of other people. Just ordering a package on Amazon, you’re manipulating the movement of many people and many robots to get a package delivered to you.

People are doing the same things to build businesses now. There’s the army of robots within servers and then there’s also an army of actual robots and people that are being manipulated through software.

## Product Leverage is Egalitarian

_The best products tend to be available to everyone_

**Product leverage is a positive-sum game**

**Naval:** Labor and capital are much less egalitarian, not just in the inputs, but in their outputs.

Let’s say that I need something that humans have to provide like if I want a massage or if I need someone to cook my food. The more of a human element there is in providing that service, the less egalitarian it is. Jeff Bezos probably has much better vacations than most of us because he has lots of humans running around doing whatever he needs to do.

If you look at the output of code and media, Jeff Bezos doesn’t get to watch better movies and TV than we do. Jeff Bezos doesn’t get to even have better computing experience. Google doesn’t give him some premium, special Google account where his searches are better.

It’s the nature of code and media output that the same product is accessible to everybody. It turns into a positive sum game where if Jeff Bezos is consuming the same product as a thousand other people, that product is going to be better than the version that Jeff would consume on his own.

**Status goods are limited to a few people**

Whereas with other products, that’s not true. If you look at something like buying a Rolex, which is no longer about telling time. It’s a signaling  good. It’s all about showing off, “I have a Rolex.” That’s a zero-sum game.

If everybody in the world is wearing a Rolex, then people don’t want to wear Rolexes anymore because they no longer signal. It’s canceled out the effect.

Rich people do have an advantage in consuming that product. They’ll just price it up until only they can have Rolexes. Then poor people can’t have Rolexes and Rolexes resume their signaling value.

**The best products tend to be targeted at the middle class**

Something like watching Netflix or using Google or using Facebook or YouTube or even frankly modern day cars. Rich people don’t have better cars. They just have weirder cars.

You can’t drive a Lamborghini on the street at any speed that makes sense for a Lamborghini, so it’s actually a worse car in the street. It just turned into a signaling good at that point. Your sweet spot, where you want to be, is somewhere like a Tesla Model 3 or like a Toyota Corolla which is an amazing car.

A new Toyota Corolla is a really nice car, but because it’s mainstream, the technology has amortized the cost of production over the largest number of consumers possible.

The best products tend to be at the center, at the sweet spot, the middle class, rather than being targeted at the upper class.

**Creating wealth with product leads to more ethical wealth**

I think one of the things that we don’t necessarily appreciate in modern societies is as the forms of leverage have gone from being human-based, labor-based and being capital-based to being more product and code and media-based, that most of the goods and services that we consume are becoming much more egalitarian in their consumption.

Even food is becoming that way. Food is becoming cheap and abundant, at least in the first world, too much so to our detriment. Jeff Bezos isn’t necessarily eating better food. He’s just eating different food or he’s eating food that’s prepared and served theatrically, so it’s almost like more of again the human element of performance.

But the labor element out of food production has gone down massively. The capital element has gone down massively. Even food production itself has become more technology-oriented, and so the gap between the haves and the have-nots is getting smaller.

If you care about ethics in wealth creation, it is better to create your wealth using code and media as leverage because then those products are equally available to everybody as opposed to trying to create your wealth through labor or capital.

**You want to use the product that is used by the most people**

What I’m referring to here is scale economies. Technology products and media products have such amazing scale economies that you always want to use the product that is used by the most people. The one that’s used by the most people ends up having the largest budget. There’s no marginal cost of adding another user, and so with the largest budget, you get the highest quality.

The best TV shows are actually not going to be some obscure ones just made for a few rich people. They’re going to be the big budget ones, like the Game of Thrones or the Breaking Bad or Bird Box, where they have massive, massive budgets. They can just use those budgets to get to a certain quality level.

Then rich people, to be different, they have to fly to Sundance and watch a documentary. You and I aren’t going to fly to Sundance because that’s something that bored rich people do to show off. We’re not going to watch a documentary because most of them just aren’t actually even that good.

Again, if you’re wealthy today, for large classes of things, you spend your money on signaling goods to show other people that you’re wealthy, then you try and convert them to status. As opposed to actually consuming the goods for their own sake.

**Nivi:** People and capital as a form of leverage have a negative externality and code and product have a positive externality attached to them, if I was going to sum up your point.

**Capital and labor are becoming permissionless**

I think that capital and labor are also starting to become a little more permissionless or at least the permissioning is diffuse because of the Internet. Instead of labor, we have community now, which is a diffused form of labor. For example, Mark Zuckerberg has a billion people doing work for him by using Facebook.

Instead of going to raise capital from someone who’s rich, now we have crowdfunding. You can raise millions and millions of dollars for a charity, for a health problem or for a business. You can do it all online.

Capital and labor are also becoming permissionless, and you don’t need to necessarily do it the old fashioned way, where you have to go around and ask people for permission to use their money or their time.

## Pick a Business Model with Leverage

_An ideal business model has network effects, low marginal costs and scale economies_

**Scale economies: the more you produce, the cheaper it gets**

**Nivi:** One more question about leverage. Do you think a choice of business model or a choice of product can also bring a kind of leverage to it?

For example, pursuing a business that has network effects. Pursuing a business that has brand effects. Or other choices of business model that people could manipulate that just give you free leverage.

**Naval:** Yeah, there’s some really good microeconomic concepts that are important to understand.

One of those is scale economies, which is the more you produce of something the cheaper it gets to make it. That’s something that a lot of businesses have, Basic Economics 101.

You should try and get into a business where making Widget Number 12 is cheaper than making Widget Number 5, and making Widget Number 10,000 is a lot cheaper than the previous ones. This builds up an automatic barrier to entry against competition and getting commoditized. That’s an important one.

**Zero marginal cost of reproduction: producing more is free**

Another one is, and this is along the same lines, but technology products especially, and media products, have this great quality where they have zero marginal cost of reproduction. Creating another copy of what you just created is free.

When somebody listens to this podcast or watches a YouTube video about this, it doesn’t cost me anything for the next person who shows up. Those zero marginal cost things, they take a while to get going because you make very little money per user, but over time they can really, really add up.

Joe Rogan is working no harder on his current podcast than he was on Podcast number 1, but on Podcast number 1,100 he’s making a million dollars from the podcast whereas for the previous one he probably lost money; for the first one. That’s an example of zero marginal cost.

**Network effects: value grows as the square of the customers**

Then, the most subtle but the most important is this idea of network effects. It comes from computer networking. Bob Metcalfe, who created Ethernet, famously coined Metcalfe’s Law, which is the value of a network is proportional to the square of the number of nodes in the network.

If a network of size 10 would have a value of a 100, a network of a size 100 would have a value of 10,000. It’s not just 10 times more, it’s 100 times more, because of the square; the difference is the square.

You want to be in a network effects business, assuming you’re not number two. If you’re number one in network effect business, you win everything. Example: if you look at Facebook, your friends and family social networking protocol. Who’s their competitor? Nobody, because they won everything through network effects. Which is why when people say, “Well, I can just switch away from Facebook,” they don’t realize that network effects create natural monopolies. They’re very, very powerful things.

**Network effect businesses are natural monopolies**

One of the dirty secrets of Silicon Valley is that a lot of the winning businesses are natural monopolies.  Even ride-sharing tends towards one winner-take-all system.

Uber will always have better economics than Lyft, as long as it’s moving more drivers and more riders around. Something like Google, there’s basically only one viable search engine. I do like DuckDuckGo, privacy reasons, but they’re just always gonna be behind because of network effects. Twitter: where else would you go for microblogging? Even YouTube has weak network effects, but they’re still powerful enough that there’s really no number two site that you go to, to consume your video on a regular basis. It even turns out in e-tail, Amazon Prime and kind of the convenience of stored credit cards and information creates a powerful network effect.

**In a network effect, each new user adds value to the existing users**

What is a network effect? Let’s just define it precisely. A network effect is when each additional user adds value to the existing user base. Your users themselves are creating some value for the existing users.

The classic example that I think everybody can understand is, language. Let’s say that there’s 100 people living in the community and speak 10 different languages, and each person just speaks one of those 10. Well, you’re having to translate all the time; it’s incredibly painful. But if all 100 of you spoke the same language, it would add tremendous value.

The way that community will play out is, 10 people start off speaking 10 languages, and let’s say one extra person learns English. Well, now all of a sudden, 11 people know English, so the next person comes in to learn a new language is probably going to chose English. At some point, let’s say English gets to 20 or 25 people, it’s done. It’s just going to own the entire language marketplace, and the rest of the languages will get competed out.

Which is why, long-term, the entire world is probably going to end up speaking English and Chinese. China’s closed off on the Internet, but the Internet itself is a great leveler, and people who want to communicate on the Internet are forced to speak English because the largest community of people on the Internet speaks English.

I always feel bad for my colleagues who grew up speaking foreign languages in foreign countries, because you don’t have access to so many books; so many books just haven’t been translated into other languages. If you only spoke French, or you only spoke German, or you only spoke Hindi, for example, you would be at a severe disadvantage in a technical education.

Invariably, if you go and get a technical education, you have to learn English just because you have to read these books that have this data that has not been translated. Languages are probably the oldest example of network effect.

Money is another example. We should all probably be using the same money, except for the fact that geographic and regulatory boundaries have created these artificial islands of money. But even then, the world tends to use a single currency as the reserve currency at most times; currently, the US dollar.

**Zero marginal cost businesses can pivot into network effect businesses**

Network effects are a very powerful concept, and when you’re picking a business model, it’s a really good idea to pick a model where you can benefit from network effects, low marginal costs, and scale economies; and these tend to go together.

Anything that has zero marginal costs of production obviously has scale economies, and things that have zero marginal costs of reproduction very often tend to have network effects, because it doesn’t cost you anything more to stamp out the thing. So then you can just create little hooks for users to add value to each other.

You should always be thinking about how your users, your customers, can add value to each other because that is the ultimate form of leverage. You’re at the beach in the Bahamas or you’re sleeping at night and your customers are adding value to each other.

## Example: From Laborer to Entrepreneur

_From low to high specific knowledge, accountability and leverage_

**Laborers get paid hourly and have low accountability**

**Naval:** The tweetstorm is very abstract. It’s deliberately meant to be broadly applicable to all kinds of different domains and disciplines and time periods and places. But sometimes it’s hard to work without a concrete example. So let’s go concrete for a minute.

Look at the real estate business. You could start at the bottom, let’s say you’re a day laborer. You come in, you fix people’s houses. Someone orders you around, tells you, “Break that piece of rock. Sand that piece of wood. Put that thing over there.”

There’s just all these menial jobs that go on, on a construction site. If you’re working one of those jobs, unless you’re a skilled trade, say, a carpenter or electrician, you don’t really have specific knowledge.

Even a carpenter or an electrician is not that specific because other people can be trained how to do it. You can be replaced. You get paid your $15, $20, $25, $50, if you’re really lucky, $75 an hour, but that’s about it.

You don’t have any leverage other than from the tools that you’re using. If you’re driving a bulldozer that’s better than doing it with your hands. A day laborer in India makes a lot less because they have no tool leverage.

You don’t have much accountability. You’re a faceless cog in a construction crew and the owner of the house or the buyer of the house doesn’t know or care that you worked on it.

**General contractors get equity, but they’re also taking risk**

One step up from that, you might have a contractor, like a general contractor who someone hires to come and fix and repair and build up their house. That general contractor is taking accountability; they’re taking responsibility.

Now let’s say they got paid $250,000 for the job. Sorry, I’m using Bay Area prices, so maybe I’ll go rest of the world prices, $100,000 for the job to fix up a house, and it actually costs the general contractor, all said and done, $70,000. That contractor’s going to pocket that remaining $30,000.

They got the upside. They got the equity but they’re also taking accountability and risk. If the project runs over and there’s losses, then they eat the losses. But you see, just the accountability gives them some form of additional potential income.

Then, they also have labor leverage because they have a bunch of people working for them. But it  probably tops out right there.

**Property developers pocket the profit by applying capital leverage**

You can go one level above that and you can look at a property developer. This might be someone who is a contractor who did a bunch of houses, did a really good job, then decided to go into business for themselves and they go around looking for beaten down properties that have potential.

They buy them, they either raise money from investors or front it themselves, they fix the place up, and then they sell it for twice what they bought it for. Maybe they only put in 20% more, so it’s a healthy profit.

So now a developer like that takes on more accountability, has more risk. They have more specific knowledge because now you have to know: which neighborhoods are worth buying in. Which lots are actually good or which lots are bad. What makes or breaks a specific property. You have to imagine the finished house that’s going to be there, even when the property itself might look really bad right now.

There’s more specific knowledge, there’s more accountability and risk, and now you also have capital leverage because you’re also putting in money into the project. But conceivably, you could buy a piece of land or a broken-down house for $200,000 and turn it into a million dollar mansion and pocket all the difference.

**Architects, large developers and REITs are even higher in the stack**

One level beyond that might be a famous architect or a developer, where just having your name on a property, because you’ve done so many great properties, increases its value.

One level up from that, you might be a person who decides, well, I understand real estate, and I now know enough of the dynamics of real estate that rather than just build and flip my own properties or improve my own properties, I’m gonna be a massive developer. I’m going to build entire communities.

Now another person might say, “I like that leverage, but I don’t want to manage all these people. I want to do it more through capital. So I’m gonna start a real estate investment trust.” That requires specific knowledge not just about investing in real estate and building real estate, but it also requires specific knowledge about the financial markets, and the capital markets, and how real estate trusts operate.

**Real estate tech companies apply the maximum leverage**

One level beyond that might be somebody who says, “Actually, I want to bring the maximum leverage to bear in this market, and the maximum specific knowledge.” That person would say, “Well, I understand real estate, and I understand everything from basic housing construction, to building properties and selling them, to how real estate markets move and thrive, and I also understand the technology business. I understand how to recruit developers, how to write code and how to build good product, and I understand how to raise money from venture capitalists and how to return it and how all of that works.”

Obviously not a single person may know this. You may pull a team together to do it where each have different skill sets, but that combined entity would have specific knowledge in technology and in real estate.

It would have massive accountability because that company’s name would be a very high risk, high reward effort attached to the whole thing, and people would devote their lives to it and take on significant risk.

It would have leverage in code with lots of developers. It would have capital with investors putting money in and the founder’s own capital. It would have labor of some of the highest quality labor that you can find, which is high quality engineers and designers and marketers who are working on the company.

Then you may end up with a Trulia or a RedFin or a Zillow kind of company, and then the upside could potentially be in the billions of dollars, or the hundreds of millions of dollars.

As you layer in more and more kinds of knowledge that can only be gained on the job and aren’t common knowledge, and you layer in more and more accountability and risk-taking, and you layer in more and more great people working on it and more and more capital on it, and more and more code and media on it, you keep expanding the scope of the opportunity all the way from the day-laborer, who might just literally be scrappling on the ground with their hands, all the way up to somebody who started a real estate tech company and then took it public.

## Judgment Is the Decisive Skill

_In an age of nearly infinite leverage, judgment is the most important skill_

**In an age of infinite leverage, judgment becomes the most important skill**

**Nivi:**  We spoke about specific knowledge, we talked about accountability, we talked about leverage. The last skill that Naval talks about in his tweetstorm is judgment, where he says, that “Leverage is a force multiplier for your judgment.”

**Naval:** We are now living in an age of nearly infinite leverage, and all the great fortunes are created through leverage. Your first job is to go and obtain leverage, and you can obtain leverage through permission by getting people to work for you, or by raising capital.

Or you can get leverage permissionlessly by learning how to code or becoming good communicator and podcasting, broadcasting, creating videos, writing, etc.

That’s how you get leverage, but once you have leverage, what do you do with it? Well, the first part of your career’s spent hustling to get leverage. Once you have the leverage, then you wanna slow down a bit, because your judgment really matters.

It’s like you’ve gone from  steering your sailboat around to now you’re steering an ocean liner or a tanker. You have a lot more at risk, but you have a lot more to gain as well. You’re carrying a much higher payload. In an age of infinite leverage, judgment becomes the most important skill.

Warren Buffett is so wealthy now because of his judgment. Even if you were to take away all of Warren’s money, tomorrow, investors would come out of the woodwork and hand him a $100 billion because they know his judgment is so good, and they would give him a big chunk of that $100 billion to invest.

**Everything else you do is setting you up to apply judgment**

Ultimately, everything else that you do is actually setting you up to apply your judgment. One of the big things that people rail on is CEO pay. For sure there’s crony capitalism that goes on where these CEOs control their boards and the boards give them too much money.

But, there are certain CEOs who definitely earned their keep because their judgment is better. If you’re steering a big ship, if you’re steering Google or Apple, and your judgment is 10 or 20 percent better than the next person’s, society will literally pay you hundreds of millions of dollars more, because you’re steering a $100 billion ship.

If you’re on course 10 or 20 percent of the time more often than the other person, the compounding results on that hundreds of billions of dollars you’re managing will be so large that your CEO pay will be dwarfed in comparison.

Demonstrated judgment, credibility around the judgment, is so critical. Warren Buffett wins here because he has massive credibility. He’s been highly accountable. He’s been right over and over in the public domain. He’s built a reputation for very high integrity, so you can trust him.

A person like that, people will throw infinite leverage behind him because of his judgment. Nobody asks him how hard he works; nobody asks him when he wakes up or when he goes to sleep. They’re like, “Warren, just do your thing.”

Judgment, especially demonstrated judgment, with high accountability, clear track record, is critical.

**Judgment is knowing the long-term consequences of your actions**

**Nivi:** Let’s define judgment. I would define it as knowing the long-term effects of your decisions, or  being able to predict the long-term effects of your decisions.

**Naval:** It’s funny. My definition of wisdom is knowing the long term consequences of your actions, so they’re not all that different. Wisdom is just judgment on a personal domain.

Wisdom applied to external problems I think is judgment. They’re highly linked. But, yes, it’s knowing the long term consequences of your actions and then making the right decision to capitalize on that.

**Without experience, judgment is often less than useless**

Judgment is very hard to build up. This is where both intellect and experience come in play.

There are many problems with the so-called intellectuals in the ivory tower, but one of the reasons why Nassim Taleb rails against them is because they have no skin in the game. They have no real-world experience, so they just apply purely intellect.

Intellect without any experience is often worse than useless because you get the confidence that the intellect gives you, and you get some of the credibility, but because you had no skin in the game, and you had no real experience, and no real accountability, you’re just throwing darts.

The real world is always far, far more complex than we can intellectualize. Especially all the interesting, fast-moving edge domains and problems, you can’t get there without experience. If you are smart and you iterate fast, it’s not even you put 10,000 hours into something, but you take 10,000 tries at something.

**The people with the best judgment are among the least emotional**

If you are smart and you have a lot of quick iterations, and you try to keep your emotions out of it, the people with the best judgment are actually among the least emotional. A lot of the best investors are considered almost robotic in that regard, but I wouldn’t be surprised if even the best entrepreneurs often come across as unemotional.

There is sort of this archetype of the passionate entrepreneur, and yeah, they have to care about what they’re doing, but they also have to see very clearly what’s actually happening. The thing that prevents you from seeing what’s actually happening are your emotions. Our emotions are constantly clouding our judgment, and in investing, or in running companies, or in building products, or being an entrepreneur, emotions really get in the way.

Emotions are what prevent you from seeing what’s actually happening, until you can no longer resist the truth of what’s happening, until it becomes too sudden, and then you’re forced into suffering; which is sort of a breaking of this fantasy that you had put together.

**Nivi:**  To try and connect some of these concepts, I would say that, first, you’re accountable for your judgment. Judgment is the exercise of wisdom. Wisdom comes from experience; and that experience can be accelerated through short iterations.

**Top investors often sound like philosophers**

**Naval:** And the reason why a lot of the top investors, a lot of the value investors, like if you read Jeremy Grantham, or you read Warren Buffet, or you read up on Michael Burry, these people sound like philosophers, or they are philosophers, or they’re reading a lot of history books or science books.

Like what are they doing, shouldn’t they be reading investment books. No. Investment books are the worst place to learn about investment, because investment is a real-world activity that is highly multi-variate, all the advantages are always being competed away. It’s always on the cutting-edge.

What you actually just need is very, very broad-based judgment and thinking. The best way to do that is to study everything, including a lot of philosophy. Philosophy also makes you more stoic, makes you less emotional, and so you make better decisions; you have better judgment.

**The more outraged someone is, the worse their judgment**

One simple thing is I see … I go out on Twitter and it seems like half of Twitter is outraged at something at all times. You can go within someone’s Twitter feed and get at least some semblance of what it must be like to be in their head all the time.

The more outraged somebody is, I guarantee you, the worse their judgment is. If someone’s constantly tweeting political outrage, and just see like an angry person getting into fights, you don’t want to hand this person the keys to your car, let alone the keys to your company.

## Set an Aspirational Hourly Rate

_Outsource tasks that cost less than your hourly rate_

**Set and enforce an aspirational hourly rate**

**Nivi:** We covered the skills you need to get rich. They included specific knowledge, accountability, leverage, judgment and life-long learning. Let’s talk about the importance of working hard and valuing your time.

**Naval:** No one is going to value you more than you value you. Set a high personal hourly rate, and stick to it. When I was young, I decided I was worth a lot more than the market thought I was worth. And I started treating myself that way.

Factor your time into every decision. Say you value your time at $100 an hour. If you decide to spend an hour driving across town to get something, you’re effectively throwing away $100. Are you going to do that?

Say you buy something from Amazon and they screw it up. Is it worth your time to return it? Is it worth the mental hassle? Keep in mind that you will have less time for work, including mentally high-output work. Do you want to use that time running errands and solving little problems? Or do you want to save it for the big stuff?

The great scientists were terrible at managing their home lives. None of them had an organized room, or made social events on time, or sent their thank-you cards.

**You can’t penny pinch your way to wealth**

You can spend your life however you want. But if you want to get rich, it has to be your top priority. It has to come before anything else, which means you can’t penny-pinch. This is what people don’t understand.

You can penny-pinch your way to basic sustenance. You can keep expenses low and maybe retire early. That’s perfectly valid. But we’re here to talk about wealth creation. If you’re going to create wealth, it has to be your number-one, overwhelming priority.

**My aspirational rate was $5,000/hr**

Fast-forward to your wealthy self and pick an intermediate hourly rate. Before I had any real money and you could hire me, I set an aspirational rate of $5,000 an hour.

Of course, I still ended up doing stupid things like arguing with the electrician or returning the broken speaker. But I shouldn’t have. And I did a lot less of it my friends. I would make a theatrical show out of throwing something in the trash or giving it to Salvation Army, rather than returning it or trying to fix it.

I would argue with girlfriends, “I don’t do that. That’s not a problem that I solve.” I still argue that today with my wife and with my mother, when she hands me little to-do’s. I say, “I would rather hire you an assistant.” This was true even when I didn’t have money.

**If you can outsource something for less than your hourly rate, do it**

Another way to think about this: If you can outsource something—or not do something—for less than your hourly rate, outsource it or don’t do it. If you can hire someone to do it for less than your hourly rate, hire them. That includes things like cooking. You may want to make your own healthy, home-cooked meals. But if you can outsource it, do that instead.

People say, “What about the joy of life? What about getting it right, just your way?” Sure, you can do that. But you’re not going to be wealthy, because you’ve made something else a priority.

[Paul Graham](https://twitter.com/paulg) said it well for [Y Combinator](https://www.ycombinator.com/) startups. He said you should be working on your product and getting product-market fit, and you should be exercising and eating healthy. That’s about it. That’s all you have time for while you’re on this mission.

**Your hourly rate should seem absurdly high**

Set a very high aspirational hourly rate for yourself, and stick to it. It should seem and feel absurdly high. If it doesn’t, it’s not high enough. Whatever you pick, my advice is to raise it.

For the longest time, I used $5,000 an hour. If you extrapolate that out as an annual salary, it’s multiple millions of dollars per year. I actually think I’ve beaten it, which is interesting given that I’m not the hardest worker. I work through bursts of energy when I’m motivated to work on something.

## Work As Hard As You Can

_Even though what you work on and who you work with are more important_

**Work as hard as you can**

**Naval:** Let’s talk about hard work. There’s a battle that happens on Twitter a lot. Should you work hard or should you not? [David Heinemeier Hansson](https://twitter.com/dhh) says, “It’s like you’re slave-driving people.” [Keith Rabois](https://twitter.com/rabois) says, “No, all the great founders worked their fingers to the bone.”

They’re talking past each other.

First of all, they’re talking about two different things. David is talking about employees and a lifestyle business. If you’re doing that, your number one priority is not getting wealthy. You have a job, a family and also your life.

Keith is talking about the Olympics of startups. He’s talking about the person going for the gold medal and trying to build a multi-billion dollar public company. That person has to get everything right. They have to have great judgment. They have to pick the right thing to work on. They have to recruit the right team. They have to work crazy hard. They’re engaged in a competitive sprint.

If getting wealthy is your goal, you’re going to have to work as hard as you can. But hard work is no substitute for who you work with and what you work on. Those are the most important things.

**What you work on and who you work with are more important**

Marc Andreessen came up with the concept of the “[product-market fit](https://pmarchive.com/guide_to_startups_part4.html).” I would expand that to “product-market-founder fit,” taking into account how well a founder is personally suited to the business. The combination of the three should be your overwhelming goal.

You can save a lot of time by picking the right area to work in. Picking the right people to work with is the next most important piece. Third comes how hard you work. They are like three legs of a stool. If you shortchange any one of them, the whole stool is going to fall. You can’t easily pick one over the other.

When you’re building a business, or a career, first figure out: “What should I be doing? Where is a market emerging? What’s a product I can build that I’m excited to work on, where I have specific knowledge?”

**No matter how high your bar is, raise it**

Second, surround yourself with the best people possible. If there’s someone greater out there to work with, go work with them. When people ask for advice about choosing the right startup to join, I say, “Pick the one that’s going to have the best alumni network for you in the future.” Look at the PayPal mafia—they worked with a bunch of geniuses, so they all got rich. Pick the people with the highest intelligence, energy and integrity that you can find.

And no matter how high your bar is, raise it.

Finally, once you’ve picked the right thing to work on and the right people, work as hard as you can.

**Nobody really works 80 hours a week**

This is where the mythology gets a little crazy. People who say they work 80-hour weeks, or even 120-hour weeks, often are just status signaling. It’s showing off. Nobody really works 80 to 120 hours a week at high output, with mental clarity. Your brain breaks down. You won’t have good ideas.

The way people tend to work most effectively, especially in knowledge work, is to sprint as hard as they can while they feel inspired to work, and then rest. They take long breaks.

It’s more like a lion hunting and less like a marathoner running. You sprint and then you rest. You reassess and then you try again. You end up building a marathon of sprints.

**Inspiration is perishable**

Inspiration is perishable. When you have inspiration, act on it right then and there.

If I’m inspired to write a blog post or publish a tweetstorm, I should do it right away. Otherwise, it’s not going to get out there. I won’t come back to it. Inspiration is a beautiful and powerful thing. When you have it, seize it.

**Impatience with actions, patience with results**

People talk about impatience. When do you know to be impatient? When do you know to be patient? My glib tweet on this was: “[Impatience with actions, patience with results.](https://twitter.com/naval/status/1008533213919133697?lang=en)” I think that’s a good philosophy for life.

Anything you have to do, get it done. Why wait? You’re not getting any younger.

You don’t want to spend your life waiting in line. You don’t want to spend it traveling back and forth. You don’t want to spend it doing things that aren’t part of your mission.

When you do these things, do them as quickly as you can and with your full attention so you do them well. Then be patient with the results because you’re dealing with complex systems and a lot of people.

It takes a long time for markets to adopt products. It takes time for people to get comfortable working with each other. It takes time for great products to emerge as you polish away.

Impatience with actions, patience with results.

If I discover a problem in one of my businesses, I won’t sleep until the resolution is at least in motion. If I’m on the board of a company, I’ll call the CEO. If I’m running the company, I’ll call my reports. If I’m responsible, I’ll get on it, right then and there, and solve it.

If I don’t solve a problem the moment it happens—or if I don’t move towards solving it—I have no peace. I have no rest. I have no happiness until the problem is solved. So I solve it as quickly as possible. I literally won’t sleep until it’s solved—maybe that’s just a personal characteristic. But it’s worked out well in business.

## Be Too Busy to ‘Do Coffee’

_Ruthlessly decline meetings_

**Be too busy to ‘do coffee’ while keeping an uncluttered calendar**

**Naval:** Another tweet was: “[You should be too busy to ‘do coffee,’ while still keeping an uncluttered calendar.](https://twitter.com/naval/status/1002108466809323521?lang=en)”

People who know me know I’m famous for simultaneously doing two things.

First, I keep a very clean calendar. I have almost no meetings on it. When some people see my calendar, they almost weep.

Second, I’m busy all the time. I’m always doing something. It’s usually work-related. It’s whatever high-impact thing that needs to be done, that I’m most inspired to do.

The only way to do that is to constantly, and ruthlessly, decline meetings.

People want to “do coffee” and build relationships. That’s fine early in your career, when you’re still exploring. But later in your career—when you’re exploiting, and there are more things coming at you than you have time for—you have to ruthlessly cut meetings out of your life.

**Ruthlessly cut meetings**

If someone wants a meeting, see if they will do a call instead. If they want to call, see if they will email instead. If they want to email, see if they will text instead. And you probably should ignore most text messages—unless they’re true emergencies.

You have to be utterly ruthless about dodging meetings. When you do meetings, make them walking meetings. Do standing meetings. Keep them short, actionable and small. Nothing is getting done in a meeting with eight people around a conference table. You are literally dying one hour at a time.

**Nivi:** “Doing coffee” reminds me of an old quote, I think from Steve Jobs, when someone asked him why Apple didn’t come to a convention. His response was something like, “Because we wouldn’t be here working.”

**Naval:** I used to have a tough time turning people down for meetings. Now I just tell them outright, “I don’t do non-transactional meetings. I don’t do meetings without a strict agenda. I don’t do meetings unless we absolutely have to.”

Nivi used to do this. When people asked us for get-to-know-you meetings, he would say, “We don’t do meetings unless it’s life-and-death urgent.” The person has to respond, “Yeah, it’s life-and-death urgent” or there’s no meeting.

**People will meet with you when you have proof of work**

Busy people will take your meeting when you have something important or valuable. But you have to come with a proper calling card. It should be: “Here’s what I’ve done. Here’s what I can show you. Let’s meet if this is useful to you, and I’ll be respectful of your time.”

You have to build up credibility. For example, when a tech investor looks at a startup, the first thing they want to see is evidence of product progress. They don’t just want to see a slide deck. Product progress is the entrepreneur’s resume. It’s an unfake-able resume.

You have to do the work. To use a crypto analogy, you have to have proof of work. If you have that and you truly have something interesting, then you shouldn’t hesitate to put it together in an email and send it. Even then, when asking for a meeting, you want to be actionable.

**Free your time and mind**

If you think you’re going to “make it” by networking and attending a bunch of meetings, you’re probably wrong. Networking can be important early in your career. And you can get serendipitous with meetings. But the odds are pretty low.

When you meet people hoping for that lucky break, you’re relying on [Type One luck](https://nav.al/money-luck), which is blind luck, and Type Two luck, which is hustle luck.

But you’re not getting Type Three or Type Four luck, which are the better kinds. This is where you spend time developing a reputation and working on something. You develop a unique point of view and are able to spot opportunities that others can’t.

A busy calendar and a busy mind will destroy your ability to do great things in this world. If you want to do great things—whether you’re a musician or entrepreneur or investor—you need free time and a free mind.

## Keep Redefining What You Do

_Become the best in the world at what you do_

**Keep redefining what you do until you’re the best at what you do**

**Nivi:** We talked about the importance of working hard and valuing your time. Next, there are a few tweets on the topic of working for the long-term. The first tweet is: “[Become the best in the world at what you do. Keep redefining what you do until this is true.](https://twitter.com/naval/status/1002108897551773697?lang=en)”

**Naval:** If you really want to get paid in this world, you want to be number one at whatever you do. It can be niche—that’s the point. You can literally get paid for just being you.

Some of the more successful people in the world are that way. Oprah gets paid for being Oprah. Joe Rogan gets paid for being Joe Rogan. They’re being authentic to themselves.

You want to be number one. And you want to keep changing what you do until you’re number one. You can’t just pick something arbitrary. You can’t say, “I’m going to be the fastest runner in the world,” and now you have to beat Usain Bolt. That’s too hard of a problem.

Keep changing your objective until it arrives at your specific knowledge, skill sets, position, capabilities, location and interests. Your objective and skills should converge to make you number one.

When you’re searching for what to do, you have two different foci to keep in mind. One is, “I want to be the best at what I do.” The second is, “What I do is flexible, so that I’m the best at it.”

You want to arrive at a comfortable place where you feel, “This is something I can be amazing at, while still being authentic to who I am.”

It’s going to be a long journey. But now you know how to think about it.

**Find founder-product-market fit**

The most important thing for any company is to find product-market fit. But the most important thing for any entrepreneur is to find founder-product-market fit, where you are naturally inclined to to build the right product for a market. That’s a three-foci problem. You have to make all three work at once.

If you want to be successful in life, you have to get comfortable managing multi-variate problems and multiple-objective functions at once. This is one of those cases where you have to map at least two or three at once.

## Escape Competition Through Authenticity

_Nobody can compete with you on being you_

**Competition will trap you in a lesser game**

**Nivi:** Let’s discuss your tweet: “[Escape competition through authenticity.](https://twitter.com/naval/status/975975798204112896?lang=en)” It sounds like part of this is a search for who you are.

**Naval:** It’s both a search and a recognition. Sometimes when we search our egos, we want to be something that we’re not. Our friends and family are actually better at telling us who we are. Looking back at what we’ve done is a better indicator of who we are.

Peter Thiel talks a lot about how [competition is besides the point](https://startupclass.samaltman.com/courses/lec05/). It’s counterproductive. We’re highly memetic creatures. We copy everybody around us. We copy our desires from them.

If everyone around me is a great artist, I want to be an artist. If everyone around me is a great businessperson, I want to be a businessperson. If everybody around me is a social activist, I want to be a social activist. That’s where my self-esteem will come from.

You have to be careful when you get caught up in status games. You end up competing over things that aren’t worth competing over.

Peter Thiel talks about how he was going to be a law clerk because everybody at law school wanted to clerk for a Supreme Court justice or some famous judge. He got rejected, and that’s what made him go into business. It helped him break out of a lesser game and into a greater game.

Sometimes you get trapped in the wrong game because you’re competing. The best way to escape competition—to get away from the specter of competition, which is not just stressful and nerve-wracking but also will drive you to the wrong answer—is to be authentic to yourself.

**No one can compete with you on being you**

If you are building and marketing something that’s an extension of who you are, no one can compete with you. Who’s going to compete with Joe Rogan or Scott Adams? It’s impossible. Is somebody else going write a better Dilbert? No. Is someone going to compete with Bill Watterson and create a better [Calvin and Hobbes](https://twitter.com/Calvinn_Hobbes?ref_src=twsrc%5Egoogle%7Ctwcamp%5Eserp%7Ctwgr%5Eauthor)? No.

Artist are, by definition, authentic. Entrepreneurs are authentic, too. Who’s going to be Elon Musk? Who’s going to be Jack Dorsey? These people are authentic, and the businesses and products they create are authentic to their desires and means.

If somebody else came along and started launching rockets, I don’t think it would faze Elon one bit. He’s still going to get to Mars. Because that’s his mission, insane as it seems. He’s going to accomplish it.

Authenticity naturally gets you away from competition. Does it mean that you want to be authentic to the point where there’s no product-market fit? It may turn out that you’re the best juggler on a unicycle. But maybe there isn’t much of a market for that, even with YouTube videos. So you have to adjust until you find product-market fit.

At least lean towards authenticity, towards getting away from competition. Competition leads to copy-catting and playing the completely wrong game.

**In entrepreneurship, the masses are never right**

In entrepreneurship, the masses are never right. If the masses knew how to build great things and create great wealth, we’d all be rich by now.

When you see a lot of competition, sometimes that indicates the masses have already arrived. It’s already competed over too much. Or it’s the wrong trend to begin with.

On the other hand, if the whole market is empty, that can be a warning indicator. It can indicate you’ve gone too authentic and should focus more on the product-market part of founder-product-market fit.

There’s a balance you have to find. Generally, people will make the mistake of paying too much attention to the competition. The great founders tend to be authentic iconoclasts.

**Combine your vocation and avocation**

**Nivi:** Do you think one way of getting to authenticity is by finding five or six various skills you already do and stacking them on top of each other, maybe not even in any purposeful way? If you are expressing who you are, you’re going to be expressing all of these skills anyway.

**Naval:** If you are successful, in the long-term you’ll find you’re almost doing all of your hobbies for a living, no matter what they are. As Robert Frost said, “[my goal in life is to unite my avocation with my vocation](https://www.goodreads.com/quotes/754-my-goal-in-life-is-to-unite-my-avocation-with)[.](https://www.goodreads.com/quotes/754-my-goal-in-life-is-to-unite-my-avocation-with)” That’s really where life is going to lead you anyway.

You’re right about the skill stack. Everyone has multiple skills. We aren’t one-dimensional creatures, even though that’s how we portray ourselves in online profiles to get employed. You meet somebody and they say, “I’m a banker.” Or, “I’m a bartender. Or “I’m a barber.”

**Specialize in being you**

But people are multivariate. They have a lot of skills. One banker might be good at finance. Another one might be good at sales. A third one might be good at macroeconomic trends and have a feel for markets. Another one might be really good at picking individual stocks. Another might be good at maintaining relationships, rather than selling new relationships. Everyone’s going to have various niches. And you’re going to have multiple niches. It’s not going to be just one.

As you go through your career, you’ll find you gravitate towards the things you’re good at, which by definition are the things you enjoy doing. Otherwise, you wouldn’t be good at them. You wouldn’t have put in the time.

Other people will push you towards the things you’re good at, too. Because your smart bosses, co-workers and investors will realize you’re world-class in this one thing. And you can recruit people to help you with other things.

Ideally, you want to end up specializing in being you.

## Play Stupid Games, Win Stupid Prizes

_Competition will blind you to greater games_

**Businesses that seem like they’re in direct competition really aren’t**

**Nivi:** When you’re being authentic, you don’t mind competition that much. It pisses you off and inspires some fear, jealousy and other emotions. But you don’t really mind because you’re oriented towards the goal and the mission. Worst-case, you might get some ideas from them. And often there are ways to work with the competition in a positive way that ends up increasing the size of the market for you.

**Naval:** It depends on the nature of the business. The best Silicon Valley tech industry businesses tend to be winner-take-all. When you see competition, it can make you fly into a rage. Because it really does endanger everything you’ve built.

If I’m opening a restaurant and a more interesting version of the same restaurant opens in a different town, that’s fantastic. I’m going to copy what’s working and drop what’s not working. So it depends on the nature of the business.

Often, businesses that seem to be in direct competition really aren’t. They end up adjacent or slightly different. You’re one step away from a completely different business, and sometimes you need to take that step. You’re not going to take it if you’re busy fighting over a booby prize.

You’re playing a stupid game. You’re going to win a stupid prize. It’s not obvious right now because you’re blinded by competition. But three years from now, it’ll be obvious.

**My first company got caught in the wrong game**

One of my first startups was [Epinions](https://www.google.com/search?q=epinions&rlz=1C1CHBF_enUS795US795&oq=Epinions&aqs=chrome.0.0l6.216j0j9&sourceid=chrome&ie=UTF-8), an online product review site that was independent of Amazon. That space eventually turned into TripAdvisor and Yelp, which is where we should have gone.

We should have done more local reviews. A review of a scarce item like a local restaurant is more valuable than one of an item like a camera that has 1,000 reviews on Amazon.

Before we could get there, we got caught up in the comparison-shopping game. We merged with DealTime and competed with a bunch of price-comparison engines—mySimon, PriceGrabber, NexTag and Bizrate, which became Shopzilla. We were caught in fierce competition with each other.

That whole space went to zero because Amazon won e-tail completely. There was no need for price comparison. Everyone just went to Amazon.

We got the booby prize because we were caught up in competition with a bunch of our peers. We should have been looking at what the consumer really wanted and being authentic to ourselves, which was reviews, not price comparison. We should have gone further into esoteric items where customers had less data and wanted reviews more badly.

If we stayed authentic to ourselves, we would have done better.

## Eventually You Will Get What You Deserve

_On a long enough timescale, you will get paid_

**On a long enough time scale, you will get paid**

**Nivi:** We’re talking about working for the long-term. The next tweet on that topic: “[Apply specific knowledge, with leverage, and eventually you will get what you deserve.](https://twitter.com/naval/status/1002103360646823936)”

I would add: Apply judgment, apply accountability, and apply the skill of reading.

**Naval:** This one is a glib way of saying, “It takes time.” Once you have all of the pieces in place, there’s still an indeterminate amount of time you have to put in. And if you’re counting, you’ll run out of patience before it arrives.

You have to make sure you give these things time. Life is long.

Charlie Munger had a line on this. Somebody asked him about making money. He said what the questioner actually was asking was, “[How can I become like you, except faster](https://www.azquotes.com/quote/922567)?”

Everybody wants it immediately. But the world is an efficient place. Immediate doesn’t work. You have to put in the time. You have to put in the hours. You have to put yourself in that position with specific knowledge, accountability, leverage and an authentic skill-set in order to be the best in the world at what you do.

And then you have to enjoy it and keep doing it and doing it and doing it. Don’t keep track. Don’t keep count. Because if you do, you will run out of time.

Looking back on my career, the people who I identified as brilliant and hardworking two decades ago are all successful now, almost without exception. On a long enough timescale, you will get paid.

But it can easily be 10 or 20 years. Sometimes it’s five. If it’s five, or three, and it’s a friend of yours who got there, it can drive you insane. But those are exceptions. And for every winner, there are multiple failures.

One thing that’s important in entrepreneurship: You just have to be right once. You get many, many shots on goal. You can take a shot on goal every three to five years, maybe every 10 at the slowest. Or once every year at the fastest, depending on how you’re iterating with startups. But you only have to be right once.

**What are you really good at, that the market values?**

**Nivi:** Your eventual outcome will be equal to something like the distinctiveness of your specific knowledge; times how much leverage you can apply to that knowledge; times how often your judgment is correct; times how singularly accountable you are for the outcome; times how much society values what you’re doing. Then you compound that with how long you can keep doing it and how long you can keep improving it through reading and learning.

**Naval:** That’s a really good way to summarize it. It’s worth trying to sketch that equation out.

That said, people try to apply mathematics to what is really philosophy. I’ve seen this happen, where I say one thing and then I say another thing that seems contradictory if you treat it as math. But it’s obviously in a different context.

People will say, “You say, ‘[Desire is suffering](https://www.google.com/search?q=desire+is+suffering&rlz=1C1CHBF_enUS795US795&oq=Desire+is+suffering&aqs=chrome.0.0j69i59l2j0l3.1077j0j7&sourceid=chrome&ie=UTF-8).’” You know, the Buddhist saying. “And then you ‘All greatness comes from suffering.’ So does that mean all greatness comes from desire?” This isn’t math. You can’t just carry variables around and form absolute logical outputs. You have to know when to apply things.

One can’t get too analytical about it.

It’s what a physicist would call “false precision.” When you take two made-up estimates and multiply them together, you get four degrees of precision. Those decimal points don’t actually count. You don’t have that data. You don’t have that knowledge. The more estimated variables you have, the greater the error in the model.

Adding more complexity to your decision-making process gets you a worse answer. You’re better off picking the single biggest thing or two. Ask yourself: What am I really good at, according to observation and people I trust, that the market values?

Those two variables alone are probably good enough. If you’re good at it, you’ll keep it up. You’ll develop the judgment. If you’re good at it and you like to do it, eventually people will give you the resources and you won’t be afraid to take on accountability. So the other pieces will fall into place.

Product-market fit is inevitable if you’re doing something you love and the market wants it.

## Reject Most Advice

_Most advice is people giving you their winning lottery ticket numbers_

**The best founders listen to everyone but make up their own mind**

**Nivi:** One of the tweets from the cutting-room floor was: “Avoid people who got rich quickly. They’re just giving you their winning lottery ticket numbers.”

**Naval:** This is generally true of most advice. It goes back to Scott Adams—[systems not goals](https://blog.dilbert.com/2013/11/18/goals-vs-systems/). If you ask a successful person what worked for them, they often read out the exact set of things that worked for them, which might not apply to you. They’re just reading you their winning lottery ticket numbers.

It’s a little glib. There is something to be learned, but you can’t take their exact circumstance and map it onto yours. The best founders I know read and listen to everyone. But then they ignore everyone and make up their own mind.

They have their own internal model of how to apply things to their situation. And they do not hesitate to discard information. If you survey enough people, all of the advice will cancel to zero.

You have to have your own point of view. When something is sent your way, you have to quickly decide: Is it true? Is it true outside of the context of how that person applied it? Is it true in my context? And then, Do I want to apply it?

You have to reject most advice. But you have to listen to enough of it, and read enough of it, to know what to reject and what to accept.

Even in this podcast, you should examine everything. If something doesn’t feel true to you, put it down. Set it aside. If too many things seem untrue, delete this podcast.

**Advice offers anecdotes to recall later, when you get your own experience**

**Nivi:** I think the most dangerous part of taking advice is that the person who gave it to you isn’t going to be around to tell you when it doesn’t apply any more.

**Naval:** I view the purpose of advice a little differently than most people. I view it as helping me have anecdotes and maxims that I can recall when I have my own direct experience and say, “Ah, that’s what that person meant.”

Ninety percent of my tweets are maxims that become mental hooks to remind me when I’m in that situation again.

Like, “Oh, I’m the one who tweeted, ‘[If you can’t see yourself working with someone for life, then don’t work with them for a day.](https://twitter.com/naval/status/511715728899473408?lang=en)’” As soon as I know I’m not going to be working with someone 10 years from now, then I have to start extricating myself from that relationship or investing less effort in it.

I use tweets to compress my own learnings. Your brain space is finite. You have finite neurons. You can think of these as pointers, addresses, mnemonics to help you remember deep-seated principles where you have the underlying experience to back it up.

If you don’t have the underlying experience, then it reads like a collection of quotes. It’s cool. It’s inspirational for a moment. Maybe you make a nice poster out of it. But then you forget it and move on.

These are compact ways for you to recall your own knowledge.

## A Calm Mind, a Fit Body, a House Full of Love

_When you’re finally wealthy, you’ll realize it wasn’t what you were seeking in the first place_

**When you’re wealthy, you’ll realize it wasn’t what you were seeking**

**Nivi:** The last tweet on the topic of working for the long-term is: “[When you’re finally wealthy, you’ll realize it wasn’t what you were seeking in the first place. But that’s for another day.](https://twitter.com/naval/status/1002109558058237953?lang=en)”

**Naval:** That’s a multi-hour topic in and of itself. First of all, I thought it was a really clever way to end the whole thing. It disarms a whole set of people who say, “What’s the point of getting rich?” There are a lot of people who like to virtue signal against the idea of wealth creation or making money.

It’s also true. Yes, money will solve all your money problems. But it doesn’t get you everywhere.

The first thing you realize when you’ve made a bunch of money is that you’re still the same person. If you’re happy, you’re happy. If you’re unhappy, you’re unhappy. If you’re calm and fulfilled and peaceful, you’re still that same person. I know lots of very rich people who are extremely out of shape. I know lots of rich people who have really bad family lives. I know lots of rich people who are internally a mess.

**A calm mind, a fit body and a house full of love must be earned**

I would lean on another tweet that I put out. When I think back on it, I think it’s my favorite tweet. It’s not necessarily the most insightful. It’s not necessarily the most helpful. It’s not even the one I think about the most. But when I look at it, there’s such a certain truth in there that it resonates. And that is: “[A fit body, a calm mind, a house full of love. These things cannot be bought—they must be earned.](https://twitter.com/naval/status/966512979066765313?lang=en)”

Even if you have all the money in the world, you can’t have those three things. Jeff Bezos still has to work out. He still has to work on his marriage. And his internal mental state still very much won’t be controlled by external events. It’s going to be based on how calm and peaceful he is inside.

So I think those three things—your health, your mental health and your close relationships—are things you have to cultivate. They can bring you a lot more peace and happiness than any amount of money ever will.

**Practical advice for a calmer internal state**

How to get there is a tweetstorm I’ve been working on. I have probably 100 tweets on it. It’s very hard to say anything on the topic without getting attacked from 50 different directions, especially these days on Twitter. So I’ve been hesitant to do it. I want to target it for a very specific kind of person.

There’s a bunch of people who don’t believe working on your internal state is useful. They’re too focused on the external. And that’s fine, there’s nothing wrong with that. That’s who the “How to Get Rich” tweetstorm is for. There’s a bunch of people who believe the only thing worth working on is complete liberation. Like, you become the Buddha. They’ll attack anything in the middle as being useless. That’s fine, too. But most people aren’t there.

I want to create a tweetstorm that offers practical advice for everyday people who want a calmer internal state. A set of understandings, realizations, half-truths and truths, that if you were to imbibe them properly—and, again, these are pointers to ideas you already have and experiences you already have—that if you keep these top of mind, slowly but steadily it will help you with certain realizations that will lead you to a calmer internal state. That’s what I want to work on.

Fitness is another big one, I’m just not the expert there. There are plenty of good people on Twitter that who are better at fitness than me.

**A lot of divorces happen over money, a lot of battles happen over internal anger**

I think a loving household and relationships actually fall naturally out of the other things. If you have a calm mind and you’ve already made money, you should have good relationships. There’s no reason why you shouldn’t. A lot of divorces happen over money. Unfortunately, that’s just the reality of it. Having money removes that part of it.

A lot of external battles happen because your internal state is not good. When you’re naturally internally peaceful you’re going to pick fewer fights. You’re going to be more loving without expecting anything in return. That will take care of things on the external-relationship front.

**Nivi:** To summarize: Money solves your money problems. Money buys you freedom in the material world. And money lets you not do the things you don’t want to do.

**Naval:** Yeah. To me the ultimate purpose of money is so you don’t have to be in a specific place, at a specific time, doing anything you don’t want to do.

## There Are No Get Rich Quick Schemes

_Get rich quick schemes are just someone else getting rich off you_

**There are no get rich quick schemes**

**Nivi:** We skipped one tweet because I wanted to cover all of the tweets on the topic of the long-term. The tweet we skipped: “[There are no get rich quick schemes. That’s just someone else getting rich off you.](https://twitter.com/naval/status/1002109022420451328?lang=en)”

**Naval:** This goes back to the world being an efficient place. If there’s an easy way to get rich, it’s already been exploited. There are a lot of people who will sell you ideas and schemes on how to make money. But they’re always selling you some $79.95 course or some audiobook or seminar.

Which is fine. Everyone needs to eat. People need to make a living. They might actually have really good tips. If they’re giving you actionable, high-quality advice that acknowledges it’s a difficult journey and will take a lot of time, then I think it’s realistic.

But if they’re selling you some get rich quick scheme—whether it’s crypto or whether it’s an online business or seminar—they’re just making money off you. That’s _their_ get rich quick scheme. It’s not necessarily going to work for you.

**We don’t have ads because it would ruin our credibility**

One of the things about this whole tweetstorm and podcast is that we don’t have ads. We don’t charge for anything. We don’t sell anything. Not because I don’t want to make more money—it’s always nice to make more money; we’re doing work here—but because it would completely destroy the credibility of the enterprise. If I say, “I know how to get rich, and I’m going to sell that to you,” then it ruins it.

When I was young, one of my favorite books on the topic was “[How To Get Rich](https://www.amazon.com/How-Get-Rich-Greatest-Entrepreneurs-ebook/dp/B0017SUYY6),” by Felix Dennis, the founder of Maxim Magazine. He had a lot of crazy stuff in there. But he had some really good insights too.

Whenever I read something by him or by GoDaddy founder Bob Parsons or Andrew Carnegie—people who are already very wealthy, and they clearly made their wealth in other fields, not by selling the how-to-get-rich line—they have a credibility. You just trust them.

They’re not trying to make money off of you. They’re obviously trying to win some status and some ego—you always have to have a motivation for doing something. But at least that’s a cleaner reason and why they’re probably not lying. They’re probably not fooling you. They’re not snowing you.

**Every founder has to lie to every employee**

At some level every founder has to lie to every employee of the company they have. They have to convince them, “It’s better for you to work for me than to do what I did and go work for yourself.”

I’ve always had a hard time with that.

The only honest way to do this, in my opinion, is to tell the entrepreneurs I recruit: “You’re going to be entrepreneurial in this company, and the day you’re ready to start your own next thing, I’m going to support you. I’m never going to get in the way of you starting a company. But this can be a good place for you to learn how to build a good team and build a good culture; how to find product-market fit; how to perfect your skills; and to meet some amazing people while you figure out exactly what it is you’re going to do. Because positioning, timing and deliberation are very important when starting a company.”

What I’ve never been able to do is to look them in the face and say, “You must be at your desk by 8 a.m.” Because I’m not going to be at my desk by 8 a.m. I want my freedom. I’ve never been able to say to them, “You’re great at being a director today, and you’ll be a VP tomorrow,” putting them into that cold career path track. Because I don’t believe in it myself.

**Anyone giving advice on how to get rich should have made their money elsewhere**

If anyone is giving advice on how to get rich and they’re also making money off of it, they should have made their money elsewhere. You don’t want to learn how to be fit from a fat person. You don’t want to learn how to be happy from a depressed person. So, you don’t want to learn how to be rich from a poor person. But you also don’t want to learn how to be rich from somebody who makes their money by telling people how to be rich. It’s suspect.

**Nivi:** Any time you see somebody who’s gotten rich following some guru’s advice on getting rich, remember that in any random process, if you run it long enough and if enough people participate in it, you will always get every single possible outcome with probability one.

**Naval:** There’s a lot of random error in there. This is why you have to absolutely and completely ignore business journalists and economist academics when they talk about private companies.

I won’t name names, but when a famous economist rails on Bitcoin, or when a business journalist attacks the latest company that’s IPO’ing, it’s complete nonsense. Those people have never built anything. They’re professional critics. They don’t know anything about making money. All they know is how to criticize and get pageviews. And you’re literally becoming dumber by reading them. You’re burning neurons.

I’ll leave you with a quote from Nassim Taleb that I liked. He said, “[To become a philosopher king, start with being a king, not being a philosopher](https://twitter.com/nntaleb/status/1112076802300755971?lang=en)[.](https://twitter.com/nntaleb/status/1112076802300755971?lang=en)”

**Nivi:** I’m glad you brought up Taleb, because I was going to finish this by saying: remember the title of his first book, “[Fooled By Randomness](https://books.google.com/books/about/Fooled_by_Randomness.html?id=DCqFYOrGyegC).”

**Naval:** One of the reasons we’re a little vague in this podcast is because we’re trying to lay down principles that are timeless, as opposed to giving you the winning lottery ticket numbers from yesterday.

## Productize Yourself

_Figure out what you’re uniquely good at, and apply as much leverage as possible_

**Figure out what you’re uniquely good at and apply as much leverage as possible** 

**Nivi:** You summarized this entire tweetstorm with two words: “[Productize yourself.](https://twitter.com/naval/status/1003356436091400192?lang=en)”

**Naval:** Productize has specific knowledge and leverage. Yourself has uniqueness and accountability. Yourself also has specific knowledge. So you can combine all of these pieces into these two words.

If you’re looking towards the long-term, you should ask yourself, “Is this authentic to me? Is it myself that I’m projecting?” And then, “Am I productizing it? Am I scaling it? Am I scaling with labor or capital or code or media?” It’s a very handy, simple mnemonic.

What is this podcast? This is a podcast called Naval. I’m literally productizing myself with a podcast.

**Nivi:** You want to figure out what you’re uniquely good at—or what you uniquely are— and apply as much leverage as possible. So making money isn’t even something you do. It’s not a skill. It’s who you are, stamped out a million times.

**Find hobbies that make you rich, fit and creative**

**Naval:** Making money should be a function of your identity and what you like to do. Another tweet I really liked was, “[Find three hobbies: One that makes you money, one that keeps you fit, and one that makes you creative.](https://www.google.com/search?client=safari&rls=en&q=Find+three+hobbies:+One+that+makes+you+money,+one+that+keeps+you+fit,+and+one+that+makes+you+creative&ie=UTF-8&oe=UTF-8)”

I would change that slightly. I would say: One that makes you money, one that makes you fit, and one that makes you smarter. So in my case, my hobbies would be reading and making money, as I love working with startups, investing in them, brainstorming them, starting them. I love the ideation and initial creation phase around startups.

On the hobby that keeps you fit, I don’t really have one. The closest thing I have is yoga, but that’s where I sort of fell apart. I think people who, early in life, discover something like surfing or swimming or tennis or some kind of a sport they continue doing throughout most of their life are very lucky, because they found a hobby that will make them fit.

## Accountability Means Letting People Criticize You

_You have to stick your neck out and be willing to fail publicly_

**Accountability means letting people criticize you**

**Nivi:** We finished discussing the tweetstorm. We’re going to spend some time on Q&A and discussing tweets that didn’t make it into the “[How to Get Rich](https://twitter.com/naval/status/1002103360646823936)” tweetstorm. My first question: What are some common failures or things people typically do wrong when they try to apply this advice?

**Naval:** A lot of people don’t understand what specific knowledge is or how to “obtain” it. People don’t understand what accountability entails. They think accountability means being successfully accountable. No—it means you have to stick your neck out and fail publicly. You have to be willing to let people criticize you.

One of the reasons I’m less active on Twitter lately is because every tweet summons an army of nitpickers and haters. It gets exhausting. You have to learn to ignore them, or you won’t survive on Twitter.

A lot of people try to reconcile this by asking, “Should I quit my 9-to-5 job or not?” That can be a hard decision. You don’t need to go to that extreme. You can start applying accountability, leverage and specific knowledge within your existing career. You don’t necessarily need to fork off and do something else completely different.

**The most interesting parts should be the ones you disagree with**

People will use my advice as a way to agree and disagree with their existing biases. They’ll say, “I agree with that part,” and, “That part you’re completely wrong.” The most interesting tweets should be the ones you disagree with—because clearly I’ve proven I know a few things. If you disagree with it, maybe that’s an area where you can improve your thinking. I improve my thinking all the time.

In this tweetstorm I put down the minimum-viable principles. I shared only a small slice of what I’ve learned about how to make money; because 90% of it is suspect.

I put down the bedrock, the stuff I’m sure about. I have not yet seen a tweet successfully contradicting anything in this tweetstorm that would cause me to say, “I got that one wrong.”

**Get the free leverage that’s available in tech**

Some people will say, “This only applies to tech entrepreneurs.” I disagree. The [real estate example](https://nav.al/laborer-tech) was a good one in that regard.

Technology drives leverage—so I’m going to push you in a tech direction to get that free leverage. Obviously, this message is being delivered through the Internet, so it’s going to have a pro-Internet bias.

**Don’t refuse to do things just because others can’t do them**

Some people believe it’s unfair to do anything with the opportunities they have because others don’t have the same opportunities. With a defeatist attitude like that, why even get out of bed in the morning? Ninety percent of people are dead.

Many people live on a dollar or less a day. Do you? No. You play the hand you’re dealt to the best of your ability. Then you can take the winnings—the pot from that hand—and do whatever you want with it to fix the world.

But if you refuse to do things just because others can’t do them, you are living in denial. It’s an excuse to do nothing.

**Realize your philanthropic vision by running a business**

Others believe wealth creation is fundamentally at odds with an environmentally healthy planet. They view it as a giant zero-sum game. That’s a false narrative, too. Elon Musk is not playing a zero-sum game with the environment; there are plenty of entrepreneurs like him.

There is a word environmentalists love: _sustainability_. If nothing else, for-profit businesses are financially sustainable. You can do a [B Corporation](https://bcorporation.net/), which has a dual mission.

Many non-profit efforts would be better off as for-profit companies. They wouldn’t have to beg for grants. They would be financially sustainable. Some great founders realize their philanthropic visions by running a business.

## We Should Eventually Be Working for Ourselves

_But we will have to make sacrifices and take on more risk_

**This advice is for anybody who wants to be entrepreneurial**

**Nivi:** Who is this advice targeted to? Is it for my Lyft driver? Is it for an Internet entrepreneur? Is it for somebody who wants to start a YouTube channel?

**Naval:** Because it comes from someone who’s steeped in Silicon Valley and tech companies, it’s always going to have a bias towards that.

But I think it’s good for anybody who wants to be entrepreneurial. Anybody who wants to control their own life. Anybody who wants to deterministically and reliably improve their ability to create wealth over time, is patient, and is looking at the long haul.

If you’re 80 years old, retired and running out of energy, it’s probably best to stay retired. But there are 80-year-olds who have a lot of energy, who want to do new things and live for the future.

Obviously this can apply very easily to a young person. I would say 9 or 10 years old and up.

**Midlife can be the most fruitful time to apply this advice**

The most difficult one is probably midlife. When we’re in our 30s, 40s and 50s, we already have a lot invested. We have a lot of obligations. Those are the years we’re earning; people are relying on us. We don’t want to change, because we don’t want to admit defeat.

But that’s when it actually can be the _most_ fruitful. It may be the most difficult pivot: You have a 9-to-5 job; you have a family relying on you.

It may seem like the things in this podcast are far too idealistic, but maybe it can inform your weekend projects. Maybe it can inform your approach to education; for example, if you’re taking an online course at night. Maybe it can inform what roles you take on at your current company, because they move you closer and closer to points of leverage, points of judgment or points where you’re naturally talented, and you’re able to be more authentic. It might cause you to take on more accountability.

Even if applied piecemeal, these principles can guide you—regardless of what stage of life you are in, short of retirement. If you’re retired, test them to see if they’re true and then teach them to your kids or grandkids.

There are many different ways to participate. It should apply to almost everybody who has a complete body, sound mind, and is looking to work.

**Look up the value chain to find leverage**

**Nivi:** One way to apply this advice is to look at who is getting leverage off of the work that you’re doing. Look up the value chain—at who’s above you and who’s above them—and see how they are taking advantage of the time and work you’re doing and how they’re applying leverage.

People naturally do this because they want to move up the corporate ladder; but that’s mostly about managing other people. You want to manage more capital, products, media and community.

People think about moving up the ladder in their organization. But they don’t often think about moving to a different organization or creating their own company to get more leverage. 

**You will do better in a small organization**

**Naval:** In general, [_ceteris paribus_](https://www.google.com/search?q=ceteris+paribus&rlz=1C1CHBF_enUS795US795&oq=ceteris+paribus&aqs=chrome..69i57j0l5.355j0j7&sourceid=chrome&ie=UTF-8)—fancy Latin words for “all other things equal”—you will do better in a smaller organization than a larger one.

You will have more accountability, and your work will be more visible. You’re more likely to be able to try different things, which can help you discover the thing you are uniquely good at. People will be more likely to give you leverage through battlefield promotions. You’ll have more flexibility. There will be more authenticity in how the company operates.

Here is a good progression for a career: Start in a large company and progressively move to smaller and smaller ones. It’s very hard to go from a small company to a larger company. Larger companies tend to be more about politics than merit; they’re more stable but less innovative.

**The goal is that we are all working for ourselves**

The long-term goal is that we are all wealthy and working for ourselves. The people working for us are essentially robots. Today that’s software robots executing code in data centers. Tomorrow it could be delivery bots, flying bots and mechanical bots—and drones—that are carrying things around. 

This goes back to the idea that the best relationships are peer relationships. If there’s someone above you, that’s someone to learn from. If you’re not learning from them and improving, nobody should be above you.

If there’s somebody below you, it’s because you’re teaching them and enabling them. If you’re not doing that, then get a robot; you don’t need a human below you.

This is utopian and still a long way off, but in the not-too-distant future anybody who wants to work for themself will be able to do it.

You may have to make sacrifices and take on more risk. You may have to take on more accountability and live with less steady income. But more and more I think younger people are realizing that if they’re going to work, they’re going to work for themselves.

## Being Ethical Is Long-Term Greedy

_If you cut fair deals, you will get paid in the long run_

**Ethics isn’t something you study; it’s something you do**

**Nivi:** In the “How to Get Rich” tweetstorm you listed things you suggest people study, like [programming, sales, reading, writing and arithmetic](https://twitter.com/naval/status/1002107869209096192). One of the items that ended up on the cutting-room floor was ethics, which you also suggest people study.

**Naval:** I was going to put that out as a concession to people who believe making money is evil and that the only way to make it is to be evil. But then I realized ethics is not necessarily something you study. It’s something you think about—and something you do.

Everyone has a personal moral code. Where we get our moral code differs for everybody. It’s not like I can point you to a textbook. I can point you to some Roman or Greek text, but that’s not suddenly going to make you ethical.

There’s the [Golden Rule](https://en.wikipedia.org/wiki/Golden_Rule): “Do unto others as you would have them do unto you.” Or there’s Nassim Taleb’s [Silver Rule](https://www.google.com/search?client=safari&rls=en&q=nassim+taleb+silver+rule&ie=UTF-8&oe=UTF-8), which is, “Don’t do unto others what you don’t want them doing unto you. ”

**Trust leads to compounding relationships**

Once you’ve been in business long enough, you will realize how much of it is about trust. It’s about trust because you want to compound interest. You want to work with trustworthy people for long periods of time without having to reevaluate every discussion or constantly look over your shoulder.

Over time you will gravitate to working with certain kinds of people. Similarly, those people will gravitate to working with other ethical people.

**Being ethical attracts other long-term players**

Acting ethically turns out to be a selfish imperative. You want to be ethical because it attracts other long-term players in the network. They want to do business with ethical people.

If you build a reputation for being ethical, people eventually will pay you just to do deals through you. Your involvement will validate deals and ensure they get done; because you wouldn’t be involved with low-quality stuff.

In the long-run, being ethical pays off—but it’s the _very_ long run. In the short-run, being unethical pays off, which is why so many people go for it. It’s short-term greedy.

**Being ethical is long-term greedy**

You can be ethical simply because you’re long-term greedy. I can even outline a framework for different parts of ethics just based on the idea of long-term selfishness.

For example, you want to be honest because it leaves you with a clear mind. You don’t want two threads running in your head, one with the lies you tell —and now have to keep track of—and the other with the truth. If you are honest, you only have to think about one thing at a time, which frees up mental energy and makes you a clearer thinker.

Also, by being honest you’re rejecting people who only want to hear pretty lies. You force those people out of your network. Sometimes it’s painful, especially with friends and family. But over the long-term you create room for the people who like you exactly the way that you are. That is a selfish reason to be honest.

**If you cut fair deals, you will get paid in the long run**

Negotiations offer another good example. If you’re the kind of person who always tries to get the best deal for yourself, you will win a lot of early deals and it will feel very good.

On the other hand, a few people will recognize that you’re always scrabbling and not acting fairly, and they will tend to avoid you. Over time those are the people who end up being the dealmakers in the network. People go to them for a fair shake or to figure out what’s fair.

If you cut people fair deals, you won’t get paid in the short-term. But over the long-term, everybody will want to deal with you. You end up being a market hub. You have more information. You have trust. You have a reputation. And people end up doing deals through you in the long-run.

A lot of wisdom involves realizing long-term consequences of your actions. The longer your time horizon, the wiser you’re going to seem to everybody around you.

## Envy Can Be Useful, or It Can Eat You Alive

_Envy can give you a powerful boost, or it can eat you alive_

**Suffering through the wrong thing can motivate you to find the right thing**

**Nivi:** Do you want to tell us about jobs you had growing up and the one that kicked off your fanatical obsession with creating wealth?

**Naval:** This gets a little personal, and I don’t want to humble-brag. There was a thread going around Twitter—_Name Five Jobs You’ve Held_—and every rich person on there was signaling how they’ve held normal jobs. I don’t want to play that game.

I’ve had menial jobs. There are people who had it worse than me and people who had it better than me.

At one point in college I was washing dishes in the school cafeteria and said, “F this. I hate this. I can’t do this anymore.” I sweet-talked may way into a teaching assistant job for a computer science professor, even though I was completely unqualified. The job forced me to learn computer algorithms, so I could TA the rest of the course.

So my desire to learn computer algorithms came out of the suffering I experienced washing dishes—not that there’s anything wrong with washing dishes; it just wasn’t for me.

I had an active mind. I wanted to make money and earn a living through mental activities, not through physical activities. Sometimes it takes suffering through the wrong thing to motivate you to find the right thing.

**Being a lawyer was not what I was meant to do**

Back in the day I had a prestigious internship at a big New York City law firm. I basically got fired for surfing [Usenet](https://en.wikipedia.org/wiki/Usenet).

This was before the Internet was a big thing. Usenet hosted newsgroups, and it was the only the only thing keeping me from being completely bored. I was an overpaid intern wearing a suit and tie. I got to hang out in the conference room and make photocopies when lawyers needed them.

I was bored out of my skull. This was pre-iPhone (thank God for Steve Jobs for saving us all from unending boredom). I used to read _The Wall Street Journal_ or anything I could get my hands on. I would’ve read the back of a brochure to keep from going insane, because listening to a bunch of corporate lawyers discuss how to optimize details of a contract is really dull.

They wanted me to sit there quietly and not read the paper. They got mad and said, “That’s rude. That’s misbehavior.”

I got called up and reprimanded a bunch of times. I was finally terminated—sent home in shame from my prestigious internship, destroying my chance to go to law school.

I was unhappy… for all of _an hour_. Ultimately, it’s one of the best things that ever happened to me. Otherwise, I would have ended up a lawyer. Not that I have anything against lawyers; it’s just not what I was meant to do.

**Envy can be useful or it can eat you alive**

**Nivi:** You mentioned a catering job that kicked off your obsession with wealth.

**Naval:** That was an envy thing. When I was in high school, I needed a job to pay for my first semester of college.

It was the summer of 1990 or 1991. This was the Bush Senior recession—if anyone listening was alive back then to remember it—so it was actually really hard to get a job.

I ended up working for a catering company serving Indian food. One day, I had to serve at a birthday party for a kid in my school. So I was out there serving food and drinks to all of my classmates. That was incredibly embarrassing. I wanted to hide away and die right there.

But you know what? It’s all part of the plan. It’s all part of the motivation. If  that didn’t happen, I probably wouldn’t be as motivated or as successful. It’s all fine. It was definitely a strong motivator.

In that sense, envy can be useful. Envy also can eat you alive if you let it follow you around your entire life. But there are points in your life when it can be a powerful booster rocket.

## Principal-Agent Problem: Act Like an Owner

_If you think and act like an owner, it’s only a matter of time until you become an owner_

**A principal is an owner; an agent is an employee**

**Nivi:** We spoke earlier about picking a business model that has [leverage from scale economies, network effects and zero marginal cost of replication](https://nav.al/business-models). There were a few other ideas on the cutting-room floor that I want to go through with you. The first one is the [principal-agent problem](https://en.wikipedia.org/wiki/Principal%E2%80%93agent_problem).

**Naval:** So [mental models](https://en.wikipedia.org/wiki/Mental_model) are all the rage. Everyone’s trying to become smarter by adopting mental models. I think mental models are interesting, but I don’t think explicitly in terms of mental-model checklists. I know [Charlie Munger](https://en.wikipedia.org/wiki/Charlie_Munger) does, but that’s just not how I think.

Instead, I tend to focus on the few lessons I’ve learned over and over in life that I think are incredibly important and seem to apply almost universally. One that keeps coming up from microeconomics—because as we’ve established, macroeconomics is not really worth spending time on—is what’s called the principal-agent problem.

In this case it’s a _principal,_ who is a person; rather than a _principle_ that you would follow. A principal is an owner. An agent works for the owner, so you can think of an agent as an employee. The difference between a founder and an employee is the difference between a principal and an agent.

**A principal’s incentives are different than an agent’s incentives**

I can summarize the principal-agent problem with a famous quote attributed to Napoleon or Julius Caesar:

“If you want it done, Go. If not, Send.”

Which is to say: If you want to do something right, do it yourself; because other people just don’t care enough.

Now, the principal-agent problem pops up everywhere. In microeconomics, they try to characterize it this way: The principal’s incentives are different than the agent’s incentives, so the owner of the business wants what is best for the business and will make the most money. The agent generally wants whatever will look good to the principal, or might make them the most friends in the neighborhood or in the business, or might make them personally the most money.

You see this a lot with hired-gun CEOs running public companies, where the ownership of the public company is distributed so widely that there’s no principal remaining. Nobody owns more than 1% of the company. The CEO takes charge, stuffs the board with their buddies and then starts issuing themself low-price stock options, or doing a lot of stock buybacks because their compensation is based directly tied to the stock price.

**If you can work on incentives, don’t work on anything else**

Agents have a way of hacking systems. This is what make incentive design so difficult. As Charlie Munger says, [if you could be working on incentives, don’t work on anything else](https://www.google.com/search?newwindow=1&rlz=1C1CHBF_enUS795US795&ei=zf8cXdbjJ8HRtQbpxIO4Bg&q=Charlie+Munger++work+on+incentives&oq=Charlie+Munger++work+on+incentives&gs_l=psy-ab.3..33i22i29i30.18324.27313..27574...2.0..0.220.3103.1j16j4......0....1..gws-wiz.......33i10j33i299j33i160.xabIqLhfyIY).

Almost all human behavior can be explained by incentives. The study of signaling is seeing what people do despite what they say. People are much more honest with their actions than they are with their words. You have to get the incentives right to get people to behave correctly. It’s a very difficult problem because people aren’t coin-operated. The good ones are not just looking for money—they’re also looking for things like status and meaning in what they do.

As a business owner you are always going to be dealing with the principal-agent problem. You’re always going to be trying to figure out: How do I make this person think like me? How do I incent them? How do I give them founder mentality?

Only founders can fully appreciate the importance of a _founder mentality_ and just how difficult and gnarly principal-agent problem is.

**When you do deals, it’s better to have the same incentives**

If you are a principal, you want to spend a lot of your time thinking about this problem. You want to be generous with your top lieutenants—in terms of ownership and incentives—even if they don’t necessarily realize it; because over time they will and you want them to be aligned with you.

When you do business deals, it’s better to have an aligned partnership where you both have the same incentives than a partnership where you have the advantage in the deal. Because eventually the other person will figure it and the partnership will fall apart. Either way, it’s not going to be one of those things that you can invest in and enjoy the benefits of compound interest over decades. 

**If you’re an employee, your most important job is to think like a principal**

Finally, if you’re in a role where you’re an agent—you’re an employee—then your most important job is to think like a principal. The more you can think like a principal, the better off you’re going to be long-term. Train yourself how to think like a principal, and eventually you will become a principal. If you align yourself with a good principal, they will promote you or empower you or give you accountability or leverage that may be way out of proportion to your relatively menial role.

I’m always impressed by founders who promote young people through the ranks and allow them to skip multiple levels despite their lack of experience. Invariably it happens because this agent—who’s way deep down in the organization—thinks like a principal. 

If you can hack your way through the principal-agent problem, you’ll probably solve half of what it takes to run a company.

**Nivi:** The reason I asked about this one first is because I feel like I never see the principal-agent problem in my work. I tend to work in small teams where everybody is highly economically aligned, and the people have been filtered for a commitment to the mission, and everybody else who doesn’t work out moves on to another role elsewhere.

**Naval:** These are all heuristics that you have designed to avoid having to deal with the single biggest problem in management.

**Deal with small firms to avoid the principal-agent problem**

Another example of a heuristic that helps you route around the principal-agent problem is to deal with the smallest firms possible. For example, when I hire a lawyer or a banker or even an accountant to work on my deals, I’ve become very cognizant about the size of the firm. Bigger firms—all other things being equal—are generally worse than small ones.

Yes, the big firm has more experience. Yes, they have more people. Yes, they have a bigger brand. But you’ll find the principal and the agent are highly separated. Very often the principal will sell you and convince you to work with the firm, but then all the work will be done by an agent who simply doesn’t care as much. You end up getting substandard service.

I prefer to work with boutiques. My ideal law firm is a law firm of one. My ideal banker is a solo banker. Now, you’re making other sacrifices and trade-offs in terms of that person’s resources—and you are betting big on that person. But you’ve got one throat to choke. There’s no one else to point fingers at; there’s nowhere to run. The accountability is extremely high.

If you are an agent, the best way to operate is to ask, “What would the founder do?” If you think like the owner and you act like the owner, it’s only a matter of time until you become the owner.

## Kelly Criterion: Avoid Ruin

_Don’t ruin your reputation or get wiped to zero_

**Don’t bet everything on one big gamble**

**Nivi:** Let’s chat about the Kelly criterion.

**Naval:** The Kelly criterion is a popularized mathematical formulation of a simple concept. The simple concept is: Don’t risk everything. Stay out of jail. Don’t bet everything on one big gamble. Be careful how much you bet each time, so you don’t lose the whole kitty.

If you’re a gambler, the Kelly criterion mathematically formulates how much you should wager per hand, even if you have an edge—because even when you have an edge, you can still lose. Let’s say you have 51-to-49 edge. Every gambler knows not to bet the whole kitty on that 51-to-49 edge—because you could lose everything and won’t get to come back to the average.

Nassim Taleb famously talks about [ergodicity](https://medium.com/incerto/the-logic-of-risk-taking-107bf41029d3), which is a fancy word for a simple concept: What is true for 100 people on average isn’t the same as one person averaging that same thing 100 times.

**Ruining your reputation is the same as getting wiped to zero**

The easiest way to see that is with [Russian roulette](https://en.wikipedia.org/wiki/Russian_roulette). Say six people play Russian roulette one time each, and each winner gets $1 billion. One person ends up dead and five people get $1 billion. Compare that to one person playing Russian roulette six times with the same gun. They are never going to end up a billionaire—they will be dead and worth zero. So risk-taking—especially when the averages that are calculated across large populations—is not always rational.

The Kelly criterion helps you avoid ruin. The number one way people get ruined in modern business is not by betting too much; it’s by cutting corners and doing unethical or downright illegal things. Ending up in an orange jumpsuit in prison or having a reputation ruined is the same as getting wiped to zero—so never do those things.

## Schelling Point: Cooperating Without Communicating

_People who can’t communicate can cooperate by anticipating the other person’s actions_

**Use social norms to cooperate when you can’t communicate**

**Nivi:** Let’s talk about Schelling points.

**Naval:** The [Schelling point](https://www.lesswrong.com/posts/yJfBzcDL9fBHJfZ6P/nash-equilibria-and-schelling-points) is a game theory concept made famous by Thomas Schelling in his book, [_The Strategy of Conflict_](https://books.google.co.id/books?id=7RkL4Z8Yg5AC&printsec=frontcover&dq=The+Strategy+of+Conflict&hl=en&sa=X&ved=0ahUKEwin7MHuw6njAhX58HMBHe2OC3YQ6AEIKjAA#v=onepage&q=The%20Strategy%20of%20Conflict&f=false), which I recommend.

It’s about multiplayer games where people respond based on what they think the other person’s response will be. He came up with a mathematical formalization to answer: How do you get people who cannot communicate with each other to coordinate?

Suppose I want to meet with you, but I don’t tell you where or when to meet. You also want to meet with me, but we can’t communicate. That sounds like an impossible problem to solve—we can’t do it. But not quite.

You can use social norms to converge on a Schelling point. I know you’re rational and educated. And you know I’m rational and educated. We’re both going to start thinking.

_When will we meet?_ If we have to pick an arbitrary date, we’ll probably pick New Year’s Eve. _What time will we meet?_ Midnight or 12:01 a.m. _Where will we meet?_ If we’re Americans, the big meeting spot is probably New York City, the most important city. _Where in New York City will we meet?_ Probably under the clock at Grand Central Station. Maybe you end up at the Empire State Building, but not likely.

**You can find Schelling points in business, art and politics**

There are many games—whether it’s business or art or politics—where you can find a Schelling point. So you can cooperate with the other person, even when you can’t communicate.

Here’s a simple example: Suppose two companies are competing heavily and hold an oligopoly. Let’s say the price fluctuates between $8 and $12 for whatever the service is. Don’t be surprised if they converge on $10 without ever talking to each other.

## Turn Short-Term Games Into Long-Term Games

_Improve your leverage by turning short-term relationships into long-term ones_

**Pareto optimal solutions require a trade-off to improve any criterion**

**Nivi:** Do you want to talk about [Pareto optimal](https://en.wikipedia.org/wiki/Pareto_efficiency)?

**Naval:** Pareto optimal is another concept from game theory, along with Pareto superior.

Pareto superior means something is better in some ways while being equal or better in other ways. It’s not worse in any way. This is an important concept when you’re negotiating. If you can make a solution Pareto superior to where it was before, you will always do that.

Pareto optimal is when the solution is the best it can possibly be and you can’t change it without making it worse in at least one dimension. There is a hard trade-off from this point forward.

These are important concepts to understand when you’re involved in a big negotiation.

**Negotiations are won by whoever cares less**

I generally say, though: “[Negotiations are won by whoever cares less](https://twitter.com/naval/status/818630258916139008?lang=en).” Negotiation is about not wanting it too badly. If you want something too badly, the other person can extract more value from you.

If someone is taking advantage of you in a negotiation, your best option is to turn it from a short-term game into a long-term game. Try to make it a repeat game. Try to bring reputation into the negotiation. Try to include other people who may want to play games with this person in the future.

An example of a high-cost, low-information single-move game is having your house renovated.

Contractors are notorious for overbooking, ripping people off, and being unaccountable. I’m sure contractors have their own side to it: “The homeowner has unreasonable demands.” “We found problems.” “The homeowner doesn’t want to pay for it.” “They don’t understand; they’re low-information buyers.”

It’s an expensive transaction. Historically it’s been very hard to find good contractors; and the contractor has little information on the homeowner.

**Convert single-move games to multi-move games**

So you try to go through friends. You try to find people with good reputations. You’re converting an expensive single-move game with a high probability of cheating on both sides into a multi-move game.

One way to do that is to say: “Actually, I need two different projects done. The first project we’ll do together, and based on that I’ll decide if we do the second project.”

Another way is to say: “I’m going to do this project with you, and I have three friends who want projects done who are waiting to see the outcome of this project.”

Another way is to write a Yelp or Thumbtack review—especially if the contractor  operates within a community and wants to protect their reputation in that community.

These are all ways to turn a single-move game into a longer term game and get past a position of poor negotiating leverage and poor information.

## Compounding Relationships Make Life Easier

_Life gets a lot easier when you know someone’s got your back_

**Mutual trust makes it easy to do business**

Relationships offer a good example of compound interest. Once you’ve been in a good relationship with somebody for a while—whether it’s business or romantic—life gets a lot easier because you know that person’s got your back. You don’t have to keep questioning.

If I’m doing a deal with someone I’ve worked with for 20 years and there is mutual trust, we don’t have to read the legal contracts. Maybe we don’t even need to _create_ legal contracts; maybe we can do it with a handshake. That kind of trust makes it very easy to do business.

If Nivi and I start another company and things aren’t working out, I know we’re both going to be extremely reasonable about deciding what to do—how to exit or shut it down. Or if we’re scaling it, how to bring in new people. We have mutual trust, and that allows us to start businesses more easily and compounds the effect.

The most under-recognized reason startups fail is because the founders fall apart.

A startup is so difficult to pull off, so removing potential friction points between founders can be the difference between success and failure.

**It’s better to have a few compounding relationships than many shallow ones**

**Nivi:** There are a couple of non-intuitive things about compounding. The first is that most of the benefits come at the end, so you may not see huge benefits up front.

Sam Altman wrote, “[I always want it to be a project that, if successful, will make the rest of my career look like a footnote](http://blog.samaltman.com/how-to-be-successful).” Again, most of the benefits of compounding come at the end.

Another thing that’s non-intuitive about compounding: It’s better to have a few deep compounding relationships than many shallow, non-compounding relationships.

**It takes just as much effort to create a small business as a large one**

**Naval:** One thing about business that people don’t realize: it takes just as much effort to create a small business as it does to create a large one.

Whether you’re Elon Musk or the guy running three Italian restaurants in town, you’re working 80 hours a week; you’re sweating bullets; you’re hiring and firing people; you’re trying to balance the books; it’s highly stressful; and it takes years and years of your life.

In one case, you get companies worth $50-$100 billion and everyone’s adulation. In the other, you might make a little bit of money and you’ve got some nice restaurants. So think big.

## Price Discrimination: Charge Some People More

_You can charge people for extras based on their propensity to pay_

**Price discrimination is a technique for charging certain people more**

**Nivi:** Are there any other microeconomic concepts, outside of [zero marginal cost of replication and scale economies](https://nav.al/business-models), that are important to understand?

**Naval:** Price discrimination is important. It means you can charge people based on their propensity to pay.

Now, you can’t charge people different amounts just because you don’t like them. You have to offer them something extra. But it has to be something rich people care about.

Business-class seats routinely cost five or 10 times more than economy seats. But it costs the airline much less—maybe two or three times more than a standard seat—to provide perks like wider seats, more legroom and free drinks.

**Rich people and large enterprises are willing to pay more**

Price discrimination works because rich people are willing to pay more. You just have to give them the extra little things they need to signal they’re rich or that little bit of comfort they want.

A lot of enterprise software companies use price discrimination, especially with [_freemium_](https://en.wikipedia.org/wiki/Freemium) products. The free or low-price version will do almost everything you want. But if you want the version that’s extra secure or hosted on your site or has multiple-user administration so the IT person can monitor everything, you’ll find yourself paying 10 or 100 times more.

## Consumer Surplus: Getting More Than You Paid For

_People are willing to pay more than what companies charge_

**Consumer surplus is the extra value you get when you pay less than you were willing**

**Naval:** [Consumer surplus and producer surplus](https://en.wikipedia.org/wiki/Economic_surplus) are important concepts. Consumer surplus is the excess value you get from something when you pay less than you were willing to pay.

I get a lot of joy out of my morning Starbucks coffee. Obviously I’ve made some money. So if my coffee cost $20, I would pay it.

But Starbucks doesn’t know that. They can’t price the coffee at $20 just for me, because they’re selling the exact same product to others. So I’m getting a lot of consumer surplus out of the coffee.

All businesses generate consumer surplus. It’s a good thing to keep in mind when someone’s harping on about how evil companies are. Amazon might be a trillion-dollar company, but I’ll bet they’re generating trillions of dollars in consumer surplus through people’s willingness to pay for convenience. A lot of people are willing to pay more than what Amazon charges.

## Net Present Value: What Future Income Is Worth Today

_See what future income is worth today by applying a discount to its future value_

**Figure out what future income is worth today by applying a discount rate**

**Nivi:** Let’s talk about [net present value](https://en.wikipedia.org/wiki/Net_present_value) (NPV).

**Naval:** Net present value is when you say, “That stream of payments I’m going to get in the future—what’s it worth today?”

Here’s a common example: You’re joining a startup and getting stock options, and the founder says, “This company is going to be worth $1 billion, and I’m giving you 0.1% of the company; therefore, you’re getting $1 million worth of stock.”

The founder is negotiating based on what it’s going to be worth in the future. You have to figure out what it’s worth today by applying a discount rate, or an interest rate, that accounts for the massive risk startups face.

You’ll end up with the amount the company is actually worth today. That’s the price at which a venture capitalist would invest in the company.

If the founder recently raised a round at a $10 million valuation, then the company’s only worth 1% of what the founder says it will be worth. So your $1 million package is actually worth $10,000. You should get very comfortable doing rough net present value calculations in your head.

## Externalities: Calculating the Hidden Costs of Products

_Externalities let you account for the true cost of products by including hidden costs_

**Nivi:** What’s a mispriced [externality](https://en.wikipedia.org/wiki/Externality)? You mentioned it on a [previous episode](https://nav.al/capitalism-intrinsic).

**Naval**: An externality is where there’s an additional cost imposed by whatever product is being produced or consumed, that’s not accounted for in the price of the product. This can happen for many reasons. Sometimes you can fix it by putting the cost back into the price.

Some of the most ardent critics of capitalism argue it’s destroying the environment. If you throw away capitalism because it’s destroying the environment, then guess what—we’re all headed back to pre-industrial times. That’s not going to be a good thing.

**Pricing externalities properly is more effective than feel-good measures**

Because the environment is finite and precious, we have to price it properly and fold that back into the cost of products and services.

If people are wasting water, releasing hydrocarbons into the atmosphere or polluting in other ways, society should charge them what it costs to clean up the pollution and return the environment to a pristine state. Perhaps that price has to be very, very, very high.

If you raise the price high enough, you’ll knock out pollution. That’s much better than feel-good measures like banning plastic bags or restricting showers during a drought.

**Properly pricing externalities can save resources in a tremendous way**

California likes to run declarations and ads to scare people into avoiding showers during droughts. It would be better to raise the price of fresh water. The average consumer might pay a few pennies more for a shower, but the almond farmers—who consume a lot of water—will cut back; and almond farming may move to a part of the country where water is more abundant.

Properly pricing externalities can save resources in a tremendous way. It’s a good framework to use when you want to do things like save the environment, rather than doing feel-good things that won’t actually amount to anything.

## Bonus: Finding Time to Invest in Yourself

_If you have to work a “normal job,” take on accountability to build your specific knowledge_

**Nivi:** A common question we get: “How do I find the time to start investing in myself? I have a job.”

**You have to rent your time to get started**

In one of the tweets from the cutting room floor, you wrote: “You will need to rent your time to get started. This is only acceptable when you are learning and saving. Preferably in a business where society does not yet know how to train people and apprenticeship is the only model.”

**Naval:** Try to learn something that people haven’t quite figured out how to teach yet. That can happen if you’re working in a new and quickly expanding field. It’s also common in fields that are circumstantial—where the details matter and it’s always moving. Investing is one of those fields; so is entrepreneurship.

Chief of staff for a founder is one of the most coveted jobs for young people starting out in Silicon Valley. The brightest kids will follow an entrepreneur around and do whatever he or she needs them to do. 

In many cases, the person is way overqualified. Someone with multiple graduate degrees might be running the CEO’s laundry because that’s the most important thing at the moment.

At the same time, that person gets to attend the most important meetings. They are privy to all the stress and theatrics, the fundraising decks and the innovation—knowledge that can only come from being in the room.

Coming out of college, Warren Buffett wanted to work for Benjamin Graham to learn to be a value investor. Buffett offered to work for free, and Graham responded, “You’re overpriced.” What that means is you have to make sacrifices to take on an apprenticeship. 

**Find the part of the job with the steepest learning curve**

If can’t learn in an apprenticeship model because you need to make money, try to be innovative in the context of your job. Take on new challenges and responsibilities. Find the part of the job with the steepest learning curve.

You want to avoid repetitive drudgery—that’s just biding time until your job is automated away.  

If you’re a barista at the coffee shop, figure out how to make connections with the customers. Figure out how to innovate the service you offer and delight the customer. Managers, founders and owners will take notice.

**Develop a founder mentality**

The hardest thing for any founder is finding employees with a founder mentality. This is a fancy way of saying they care enough. 

People will say, “Well, I’m not the founder. I’m not being paid enough to care.” Actually, you are: The knowledge and skills you gain by developing a founder mentality set you up to be a founder down the line; that’s your compensation. 

You can get a lot out of almost any position. You just have to put a lot into it.

**Accountability is something you can take on immediately**

**Nivi:** We’ve discussed accountability, judgment, specific knowledge and leverage. If I’m working a “normal” job, is specific knowledge the one I should focus on? 

**Naval:** Judgment takes experience. It takes a lot of time to build up. You have to put yourself in positions where you can exercise judgment. That’ll come from taking on accountability.

Leverage is something that society gives you after you’ve demonstrated judgment. You can get it faster by learning high-leverage skills like coding or working with the media. These are _permissionless_ leverage. This is why I encourage people to learn to code or produce media, even if it’s just nights and weekends. 

So, judgment and leverage tend to come later. Accountability is something you can take on immediately. You can say, “Hey, I’ll take charge of this thing that nobody wants to take charge of.” When you take on accountability, you’re also publicly putting your neck on the chopping block—so you have to deliver. 

You build specific knowledge by taking accountability for things that other people don’t know how to do. Perhaps they’re things you enjoy doing or are naturally inclined towards doing anyway.

If you work in a factory, the hardest thing may be raising capital to keep the factory running. Maybe that’s what the owner is always stressed out about.

You might notice this and think, “I’m good at balancing my checkbook and have a good head for numbers; but I haven’t raised money before.” You offer to help and become the owner’s sidekick solving their fundraising problem. If you have a natural aptitude and take on accountability, you can put yourself in a position to learn quickly. Before long, you’ll become the heir apparent.

Early on, find things that interest you and allow you to take on accountability. Don’t worry about short-term compensation. Compensation comes when you’re tired of waiting for it and have given up on it. This is the way the whole system works. 

If you take on accountability and solve problems on the edge of knowledge that others can’t solve, people will line up behind you. The leverage will come.

**Specific knowledge can be timely or timeless**

There are two forms of specific knowledge: _timely_ and _timeless_. 

If you become a world-class expert in machine learning just as it takes off and you got there through genuine intellectual interest, you’re going to do really well. But 20 years from now, machine learning may be second hat; the world may have moved on to something else. That’s _timely_ knowledge.

If you’re good at persuading people, it’s probably a skill you picked up early on in life. It’s always going to apply, because persuading people is always going to be valuable. That’s _timeless_ knowledge.

Now, persuasion is a generic skill—it’s probably not enough to build a career on. You need to combine it in a skill stack, as [Scott Adams writes](https://www.scottadamssays.com/2016/11/28/the-trump-talent-stack/). You might combine persuasion with accounting and an understanding of semiconductor production lines. Now you can become the best semiconductor salesperson and, later on, the best semiconductor company CEO.

_Timeless_ specific knowledge usually can’t be taught, and it sticks with you forever. _Timely_ specific knowledge comes and goes; but it tends to have a fairly long shelf life.

Technology is a good place to find those timely skills sets. If you can bring in a more generic specialized knowledge skill set from the outside, then you’ve got gold.

**Technology is an intellectual frontier for gaining specific knowledge**

**Nivi:** There were a couple other tweets from the cutting-room floor on this topic. The first: “The technology industry is a great place to acquire specific knowledge. The frontier is always moving forward. If you are genuinely intellectually curious, you will acquire the knowledge ahead of others.”

**Naval:** [Danny Hillis](https://twitter.com/dannyhillis?lang=en) famously said technology is [everything that doesn’t work yet](https://kk.org/thetechnium/everything-that/). Technology is around us everywhere. The spoon was technology once; fire was technology once. When we figured out how to make them work, they disappeared in the background and became part of our everyday lives. 

Technology is, by definition, the intellectual frontier. It’s taking things from science and culture that we have not figured out how to mass produce or create efficiently and figuring out how to commercialize it and make it available to everybody.

Technology will always be a great field where you can pick up specific knowledge that is valuable to society.

**If you don’t have accountability, do something different**

**Nivi:** Here’s another tweet from the cutting-room floor related to accountability: “Companies don’t know how to measure outputs, so they measure inputs instead. Work in a way that your outputs are visible and measurable. If you don’t have accountability, do something different.”

**Naval:** The entire structure of rewarding people comes from the agricultural and industrial ages, when inputs and outputs matched up closely. The amount of hours you put into doing something was a reliable proxy for what kind of output you’d get. 

Today, it’s extremely nonlinear. One good investment decision can make a company $10 million or $100 million. One good product feature can be the difference between product-market fit and complete failure.

As a result, judgment and accountability matter much more. Often the best engineers aren’t the hardest workers. Sometimes they don’t work very hard at all, but they dependably ship that one critical product at just the right time. It’s similar to the salesperson who closes the huge deal that makes the company’s numbers for the quarter.

People need to be able to tell what role you had in the company’s success. That doesn’t mean stomping all over your team—people are extremely sensitive to others taking too much credit. You always want to be giving out credit. Smart people will know who was responsible.

Some jobs are too removed from the customer for this type of accountability. You’re just a cog in a machine.  

Consulting is a good example. As a consultant, your ideas are delivered through someone else within the organization. You may not have visibility to the top people; you may be hidden behind a screen. That’s a trade-off you’re making in exchange for your independence.

**You’ll develop thick-skin if you take on accountability**

When you have accountability, you get a lot more credit when things go right. Of course, the downside is you get hurt a lot more when things go wrong. When you stick your neck out, you have to be willing to be blamed, sacrificed and even attacked.

If you’re the kind of person who thrives in a high-accountability environment, you’re going to end up thick-skinned over time. You’re going to get hurt a lot. People are going to attack you if you fail.

**Scale your specific knowledge with apprentices**

**Nivi:** Once you get some specific knowledge, you can scale it by training your own apprentices and outsourcing tasks to them.

**Naval:** For example, I made good investments and figured out the venture business. I could have kept doing that and making money. Instead, I co-founded Spearhead to train up-and-coming founders to become angels and venture investors. We give them a checkbook to start investing.

It’s an apprenticeship model. They come to us with deals they’re looking at, and we help them think it through. This model is more scalable than my personal investing.

**Specific knowledge comes on the job, not in a classroom**

At Spearhead we lead classes teaching founders about investing, and we also hold office hours to discuss specific deals they bring.

It turns out the classes and talks we lead are largely worthless. You can give all the generic advice people need in about an hour. After that, the advice gets so circumstantial that it essentially cancels to zero. But the office hours are incredibly useful. 

This reinforces the notion that investing is one of those skills that can only be learned on the job. When you find a skill like that, you’re dealing with specific knowledge.

Another good indicator of specific knowledge is when someone can’t give a straight answer to the question: “What do you do every day?” Or you get an answer along the lines of, “Every day is different based on what’s going on.”

The thing is so complicated and dependent upon circumstances that it can’t be boiled down into a textbook form.

**Nivi:** The mafia figured out this apprenticeship model a long time ago. The best way to end up running one of the families was to become the driver for the Don.

**Naval:** [Tony Soprano](https://www.google.com/search?q=Tony+Soprano&ie=UTF-8&oe=UTF-8&hl=en-us&client=safari) was a businessman who had to enforce his own contracts. That’s a very complicated business.

_This transcript has been edited for clarity._
