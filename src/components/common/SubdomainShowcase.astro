---
// SubdomainShowcase.astro - 展示 moatkon.com 的子域名和主要内容
---

<div class="subdomain-showcase">
  <div class="showcase-header">
    <h2 class="showcase-title">
      <span class="domain-main">moatkon.com</span>
      <span class="domain-subtitle">生态系统</span>
    </h2>
    <p class="showcase-description">探索护城河的不同维度</p>
  </div>
  
  <div class="subdomain-grid">
    <a href="https://blog.moatkon.com" class="subdomain-card blog-card" target="_blank" rel="noopener noreferrer">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 9H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 13H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 17H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="card-content">
        <h3 class="card-title">Blog</h3>
        <p class="card-description">技术思考与生活感悟</p>
        <span class="card-url">blog.moatkon.com</span>
      </div>
      <div class="card-arrow">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 17L17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M7 7H17V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </a>

    <a href="https://resume.moatkon.com" class="subdomain-card resume-card" target="_blank" rel="noopener noreferrer">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M10 9H9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="card-content">
        <h3 class="card-title">Resume</h3>
        <p class="card-description">专业履历与技能展示</p>
        <span class="card-url">resume.moatkon.com</span>
      </div>
      <div class="card-arrow">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 17L17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M7 7H17V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </a>

    <a href="https://podcast.moatkon.com" class="subdomain-card podcast-card" target="_blank" rel="noopener noreferrer">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 1C8.13401 1 5 4.13401 5 8C5 10.7614 6.5 13.1667 8.75 14.5V17C8.75 17.6904 9.30964 18.25 10 18.25H14C14.6904 18.25 15.25 17.6904 15.25 17V14.5C17.5 13.1667 19 10.7614 19 8C19 4.13401 15.866 1 12 1Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M19 11C20.1046 11 21 11.8954 21 13V15C21 16.1046 20.1046 17 19 17H18V11H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M5 11C3.89543 11 3 11.8954 3 13V15C3 16.1046 3.89543 17 5 17H6V11H5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M10 21H14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="card-content">
        <h3 class="card-title">Podcast</h3>
        <p class="card-description">程序员的生活记录</p>
        <span class="card-url">podcast.moatkon.com</span>
      </div>
      <div class="card-arrow">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 17L17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M7 7H17V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </a>
  </div>
</div>

<!-- 主要内容区域 -->
<div class="main-content-section">
  <div class="content-header">
    <h2 class="content-title">核心领域</h2>
    <p class="content-description">专业技能与兴趣爱好的深度探索</p>
  </div>

  <div class="content-grid">
    <a href="/software-engineer/readme" class="content-card engineer-card">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="card-content">
        <h3 class="card-title">软件工程师</h3>
        <p class="card-description">优雅且高效地处理数据</p>
      </div>
      <div class="card-arrow">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 17L17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M7 7H17V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </a>

    <a href="/english" class="content-card english-card">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" fill-opacity="0.1"/>
        </svg>
      </div>
      <div class="card-content">
        <h3 class="card-title">英语</h3>
        <p class="card-description">最好的语言不是Java、C++之类的,而是英语</p>
      </div>
      <div class="card-arrow">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 17L17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M7 7H17V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </a>

    <a href="/music/piano/study" class="content-card piano-card">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M6 19H18V5H6V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M10 5V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M14 5V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M8 5V12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 5V12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M16 5V12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="card-content">
        <h3 class="card-title">钢琴🎹</h3>
        <p class="card-description">因为我熟悉键盘,所以我选择了带按键的乐器</p>
      </div>
      <div class="card-arrow">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 17L17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M7 7H17V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </a>

    <a href="/share" class="content-card share-card">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 8C19.6569 8 21 6.65685 21 5C21 3.34315 19.6569 2 18 2C16.3431 2 15 3.34315 15 5C15 6.65685 16.3431 8 18 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M6 15C7.65685 15 9 13.6569 9 12C9 10.3431 7.65685 9 6 9C4.34315 9 3 10.3431 3 12C3 13.6569 4.34315 15 6 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M18 22C19.6569 22 21 20.6569 21 19C21 17.3431 19.6569 16 18 16C16.3431 16 15 17.3431 15 19C15 20.6569 16.3431 22 18 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M8.59 13.51L15.42 17.49" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M15.41 6.51L8.59 10.49" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="card-content">
        <h3 class="card-title">分享</h3>
        <p class="card-description">效率工具、博主、电影🎬、视频、网站、音乐🎵</p>
      </div>
      <div class="card-arrow">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 17L17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M7 7H17V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </a>

    <a href="/ai" class="content-card ai-card">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="card-content">
        <h3 class="card-title">AI</h3>
        <p class="card-description">Artificial intelligence,智能或者说智慧,我理解的就是概率</p>
      </div>
      <div class="card-arrow">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 17L17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M7 7H17V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </a>
  </div>
</div>

<!-- 智慧语录区域 -->
<div class="quotes-section">
  <div class="quotes-header">
    <h2 class="quotes-title">智慧语录</h2>
    <p class="quotes-description">值得深思的人生感悟</p>
  </div>

  <div class="quotes-grid">
    <div class="quote-card growth-quote">
      <div class="quote-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" fill-opacity="0.1"/>
        </svg>
      </div>
      <div class="quote-content">
        <h3 class="quote-title">"Does this help growth or not?"</h3>
        <p class="quote-text">"这是否有助于生长？"</p>
        <p class="quote-description">可以经常问问自己这段时间做的事情,是否有利于增长,至于增长的是什么?可以是余额、技能、睡眠时间等等。确保自己所做的事情是保持正向收益的</p>
        <div class="quote-source">
          <span class="source-label">源:</span>
          <a href="/english/internet/what-i-learned-working-for-mark-zuckerberg" class="source-link">What I Learned Working For Mark Zuckerberg</a>
        </div>
      </div>
    </div>

    <div class="quote-card wisdom-quote">
      <div class="quote-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 3H8C9.06087 3 10.0783 3.42143 10.8284 4.17157C11.5786 4.92172 12 5.93913 12 7V21C12 20.2044 11.6839 19.4413 11.1213 18.8787C10.5587 18.3161 9.79565 18 9 18H2V3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M22 3H16C14.9391 3 13.9217 3.42143 13.1716 4.17157C12.4214 4.92172 12 5.93913 12 7V21C12 20.2044 12.3161 19.4413 12.8787 18.8787C13.4413 18.3161 14.2044 18 15 18H22V3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="quote-content">
        <h3 class="quote-title">书里面的一句话</h3>
        <p class="quote-text">"生存法则很简单，就是忍人所不忍，能人所不能。忍是一条线，能是一条线，两者的间距就是生存机会。"</p>
        <p class="quote-description">这句话深刻地阐述了在竞争激烈的环境中脱颖而出的关键——超越常人的忍耐力和能力。</p>
        <div class="quote-source">
          <span class="source-placeholder"></span>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .subdomain-showcase {
    margin: 2rem 0;
    padding: 0;
  }

  .showcase-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .showcase-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--sl-color-text);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }

  .domain-main {
    background: linear-gradient(135deg, var(--sl-color-accent), var(--sl-color-accent-high));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: 800;
    letter-spacing: -0.02em;
  }

  .domain-subtitle {
    font-size: 1rem;
    font-weight: 500;
    color: var(--sl-color-gray-3);
    letter-spacing: 0.05em;
  }

  .showcase-description {
    color: var(--sl-color-gray-3);
    font-size: 1rem;
    margin: 0;
    font-weight: 400;
  }

  .subdomain-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
  }

  .subdomain-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: var(--sl-color-gray-7, var(--sl-color-gray-6));
    border: 1px solid var(--sl-color-hairline-light);
    border-radius: 0.75rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    gap: 1rem;
  }

  .subdomain-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--card-accent), transparent);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .subdomain-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--card-accent);
  }

  .subdomain-card:hover::before {
    opacity: 1;
  }

  .card-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--card-accent-bg);
    border-radius: 0.5rem;
    color: var(--card-accent);
    transition: all 0.2s ease;
  }

  .subdomain-card:hover .card-icon {
    transform: scale(1.1);
    background: var(--card-accent);
    color: white;
  }

  .card-content {
    flex: 1;
    min-width: 0;
  }

  .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: var(--sl-color-text);
  }

  .card-description {
    font-size: 0.875rem;
    color: var(--sl-color-gray-3);
    margin: 0 0 0.5rem 0;
    line-height: 1.4;
  }

  .card-url {
    font-size: 0.75rem;
    color: var(--card-accent);
    font-weight: 500;
    font-family: var(--sl-font-mono, monospace);
    letter-spacing: 0.02em;
  }

  .card-arrow {
    flex-shrink: 0;
    color: var(--sl-color-gray-4);
    transition: all 0.2s ease;
  }

  .subdomain-card:hover .card-arrow {
    color: var(--card-accent);
    transform: translate(2px, -2px);
  }

  /* 为每个卡片定义不同的主题色 */
  .blog-card {
    --card-accent: var(--color-blue-500);
    --card-accent-bg: var(--color-blue-100);
  }

  .resume-card {
    --card-accent: var(--color-green-500);
    --card-accent-bg: var(--color-green-100);
  }

  .podcast-card {
    --card-accent: var(--color-purple-500);
    --card-accent-bg: var(--color-purple-100);
  }

  /* 主要内容卡片的主题色 */
  .engineer-card {
    --card-accent: var(--color-blue-600);
    --card-accent-bg: var(--color-blue-100);
  }

  .english-card {
    --card-accent: var(--color-orange-500);
    --card-accent-bg: var(--color-orange-100);
  }

  .piano-card {
    --card-accent: var(--color-magenta-500);
    --card-accent-bg: var(--color-magenta-100);
  }

  .share-card {
    --card-accent: var(--color-green-600);
    --card-accent-bg: var(--color-green-100);
  }

  .ai-card {
    --card-accent: var(--color-purple-600);
    --card-accent-bg: var(--color-purple-100);
  }

  /* 智慧语录卡片的主题色 */
  .growth-quote {
    --quote-accent: var(--color-orange-600);
    --quote-accent-light: var(--color-orange-400);
    --quote-accent-bg: var(--color-orange-100);
  }

  .wisdom-quote {
    --quote-accent: var(--color-red-600);
    --quote-accent-light: var(--color-red-400);
    --quote-accent-bg: var(--color-red-100);
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .subdomain-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .subdomain-card {
      padding: 1.25rem;
    }
    
    .showcase-title {
      font-size: 1.5rem;
    }
    
    .domain-main {
      font-size: 1.75rem;
    }
  }

  /* 主要内容区域 */
  .main-content-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--sl-color-hairline-light);
  }

  .content-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .content-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--sl-color-text);
  }

  .content-description {
    color: var(--sl-color-gray-3);
    font-size: 0.95rem;
    margin: 0;
    font-weight: 400;
  }

  .content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
  }

  .content-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: var(--sl-color-gray-7, var(--sl-color-gray-6));
    border: 1px solid var(--sl-color-hairline-light);
    border-radius: 0.75rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    gap: 1rem;
  }

  .content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--card-accent), transparent);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--card-accent);
  }

  .content-card:hover::before {
    opacity: 1;
  }

  .content-card .card-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--card-accent-bg);
    border-radius: 0.5rem;
    color: var(--card-accent);
    transition: all 0.2s ease;
  }

  .content-card:hover .card-icon {
    transform: scale(1.1);
    background: var(--card-accent);
    color: white;
  }

  .content-card .card-content {
    flex: 1;
    min-width: 0;
  }

  .content-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: var(--sl-color-text);
  }

  .content-card .card-description {
    font-size: 0.875rem;
    color: var(--sl-color-gray-3);
    margin: 0;
    line-height: 1.4;
  }

  .content-card .card-arrow {
    flex-shrink: 0;
    color: var(--sl-color-gray-4);
    transition: all 0.2s ease;
  }

  .content-card:hover .card-arrow {
    color: var(--card-accent);
    transform: translate(2px, -2px);
  }

  /* 智慧语录区域 */
  .quotes-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--sl-color-hairline-light);
  }

  .quotes-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .quotes-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--sl-color-text);
  }

  .quotes-description {
    color: var(--sl-color-gray-3);
    font-size: 0.95rem;
    margin: 0;
    font-weight: 400;
  }

  .quotes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    align-items: start; /* 确保所有卡片顶部对齐 */
  }

  .quote-card {
    display: flex;
    flex-direction: column;
    padding: 2rem;
    background: var(--sl-color-gray-7, var(--sl-color-gray-6));
    border: 1px solid var(--sl-color-hairline-light);
    border-radius: 1rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    min-height: 320px; /* 设置最小高度确保一致性 */
    height: 100%; /* 填充网格容器的完整高度 */
  }

  .quote-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--quote-accent), var(--quote-accent-light));
    opacity: 0.8;
  }

  .quote-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--quote-accent);
  }

  .quote-icon {
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--quote-accent-bg);
    border-radius: 1rem;
    color: var(--quote-accent);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
  }

  .quote-card:hover .quote-icon {
    transform: scale(1.1) rotate(5deg);
    background: var(--quote-accent);
    color: white;
  }

  .quote-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* 确保内容在卡片中均匀分布 */
  }

  .quote-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    color: var(--sl-color-text);
    line-height: 1.3;
  }

  .quote-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--quote-accent);
    margin: 0 0 1rem 0;
    line-height: 1.5;
    font-style: italic;
    position: relative;
    padding-left: 1rem;
  }

  .quote-text::before {
    content: '"';
    position: absolute;
    left: 0;
    top: -0.2rem;
    font-size: 2rem;
    color: var(--quote-accent);
    opacity: 0.3;
  }

  .quote-description {
    font-size: 0.95rem;
    color: var(--sl-color-gray-3);
    margin: 0 0 1.5rem 0;
    line-height: 1.6;
    flex: 1; /* 让描述区域占据剩余空间 */
  }

  .quote-source {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: auto; /* 始终推到底部 */
    padding-top: 1rem; /* 与上方内容保持间距 */
  }

  .source-label {
    font-size: 0.85rem;
    color: var(--sl-color-gray-4);
    font-weight: 500;
  }

  .source-link {
    font-size: 0.85rem;
    color: var(--quote-accent);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .source-link:hover {
    color: var(--quote-accent-light);
    text-decoration: underline;
  }

  .source-placeholder {
    height: 1.2rem; /* 与source-link相同的高度 */
    visibility: hidden; /* 不可见但占据空间 */
  }

  /* 暗色模式适配 */
  :root[data-theme='light'] {
    .blog-card {
      --card-accent-bg: var(--color-blue-50);
    }

    .resume-card {
      --card-accent-bg: var(--color-green-50);
    }

    .podcast-card {
      --card-accent-bg: var(--color-purple-50);
    }

    .engineer-card {
      --card-accent-bg: var(--color-blue-50);
    }

    .english-card {
      --card-accent-bg: var(--color-orange-50);
    }

    .piano-card {
      --card-accent-bg: var(--color-magenta-50);
    }

    .share-card {
      --card-accent-bg: var(--color-green-50);
    }

    .ai-card {
      --card-accent-bg: var(--color-purple-50);
    }

    .growth-quote {
      --quote-accent-bg: var(--color-orange-50);
    }

    .wisdom-quote {
      --quote-accent-bg: var(--color-red-50);
    }
  }
</style>
